{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:21'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:21'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:23'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:23'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:26'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:26'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:27'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:27'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:46:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:30:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:46:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http%3A%2F%2Flocalhost%3A3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:30:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:51:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:31:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:115:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQAS_iV7NOJo_tCiaowxNmmN0pSGFlL0SvntCWC-96w_XgXoVitw7EnqlUX3FfYeK04n9rX-sQ6LLAEw_pYpTzj8fC5gE82fVoSdZW_CafbxxXK6QsXz-lUDFM8K1JHB8w0Shhj7ssNKmNQHJ6KK5XYDD1yCzAOK1AZReuJv8TvRWhtZpyjZsGMwuT6pkBlDmzfTKmVG1XNJKVqqJbNV9WPNq7PWf2pQA30ftXbKkhmrgjzVj-HMWJWiI9XUPQIGHTze1j73HMenS8VCxHtL82fU0WP688UVXDOk-ds82InlUuaXfyhz__JPtxJOm7mscuwTCMtIfCQUrhV9t5OsTR7Klt0QgrsZTynsif9qtL1N_7MFkixVCGsXPCro44gkRwOzjEQC4okWzzTUUwNnmVlg',
    state: '7725da2de3ac835210975689b0561035aa60f628b4347a5bce3d5300018c12bb',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 17:33:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:115:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQDkU2DQi8yL0lp-8P12S7TCoM15XG7qwDRh_AvEM7cNEf8G0nbYvVRC0EjReMK6FugdNnOMafjaZ49J5jd80afcxNBMlL2tS-e_z6K5QJOB3ikJEFZ8N06GhUVZa0FvmhoGddH16r2xDyCGZZlp7O0naMeIsvWA_zC3685eb8BeOSOq0_t1rSOibtqtCmlvyFhKqJjuwC5niqntG-T_x9MCcV_NcXZqIDGYmhXumcHBvY6z2hKrYcVbTnqK2xIxeiOjI9Qq2vR00Tfda_Nga8ldH0G8ctsZ0OiVmWz1Q_El0f3O1UCnyK8ipJQpRGhUwFCRvu7ntrVnRtLv_9CWq1cuxvXxyIkkdppZDBziGGftfpxZHAmt-oTnLe3diWOgMjFmceUJv0WhqQYwO9OjpB3R',
    state: '791016d9ee0e769ce38730835e116229f8ae58455ff9ef1b726116d055460921',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 17:34:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:32'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:32'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:34'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:34'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:35'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:35'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:37'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:37'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:40'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:42'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:42'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:47'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:47'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:51'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:51'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:55'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:55'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:56'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:56'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:57'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:57'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:58'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:02'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:02'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:04'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:04'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:05'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:05'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:06'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:06'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:07'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:07'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:08'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:08'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:10'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:10'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:11'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:11'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:12'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:12'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:13'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:13'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'error',
  message: 'Failed to initialize server: app.use() requires a middleware function',
  stack: 'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\application.js:217:11)\n' +
    '    at PressureMaxServer.setupErrorHandling (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:221:14)\n' +
    '    at PressureMaxServer.initialize (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:76:12)\n' +
    '    at async PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:281:5)',
  timestamp: '2025-06-25 18:19:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'error',
  message: 'Failed to initialize server: app.use() requires a middleware function',
  stack: 'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\application.js:217:11)\n' +
    '    at PressureMaxServer.setupErrorHandling (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:221:14)\n' +
    '    at PressureMaxServer.initialize (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:76:12)\n' +
    '    at async PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:281:5)',
  timestamp: '2025-06-25 18:22:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000,
  level: 'error',
  message: 'Uncaught Exception: listen EADDRINUSE: address already in use :::3000',
  stack: 'Error: listen EADDRINUSE: address already in use :::3000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n' +
    '    at listenInCluster (node:net:1994:12)\n' +
    '    at Server.listen (node:net:2099:7)\n' +
    '    at PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:286:17)',
  timestamp: '2025-06-25 18:24:40'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:116:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQDBUrkjEzpaZ0vUl8zfT1XOVa_3CUe0sjRFXvBmvtFp-BTt3qVeOiU2SvA74Rj6NJ4bq2KoDsiT03InhhpMj1I18sXuAxykU8VHXzV2I-Ly4PbWThi4U8uaagr7jzAqCmqy48atpTfEUi8-iWbJOgZDD4kCTyEW4w8boTt_PGg5nJiWwIE_7369rT9SBLfSyKF_WA01JXhe5NZqxa2wjKroww_3_2LLzssmQ20NKnJnJMXvqRZCCjrt49M3tTUCxrevqiqcB9j5pkKWudp8ew0OejryxBGJcVExrYRpaf1LufIP0YeFEFnFboWa0lgFEpgjiSl8SqLuSDh9mDEs8v_B91jLI0_8P49M1AmjVQQITldyf5Hfdx9gJ0NjPJnhs-QZo8VTJrOUOH5nwesk5Zjj',
    state: 'e38f33e22830529f74eeab5b276a29e14f002b8f95cb59140971f19870089201',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:45:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:116:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQCPOhA8gu_deRTB7v5T7k7PvYgGUIdmdH2PnjW5yiTW0PNMZjOVpZQfE2gcymygsvVJWQeYQ6DIq2z5dzWPEswy0XaC8rT23sW2eiNNerMZVCNNvTO8X1fqWKk_rbhctvJICgG4bIZcAxcKB_oQCxHvxLeYKviYOyUiGMs_dRS7yTa7_EGfG9vUNdzW6nvUNP-UjI4R_INpgnkH09leiuDdHNzJ9U8NEby07JLEG4B0UK4DsxtbA6ZoKHMFNMqp_D_lIizgwBg3MHYUVdJjw0bA7K1sdYh8tcRPnM3Hja4Z39bnpcLm5CxBAh4QVowbdNVuNNNNhaTMvWMrSSEmjnfj8HHDPMtWYuZ63rduUnjbcB_IbjvSZsXhcvkZiPDoSGN7SmfSB5Rfm8z1S6OnkLIv',
    state: '5e185b7efcfcb79bdee2a3ca3c0d9639eb032d2b396c405452cc14aa52c57a27',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:48:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:116:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {
    code: 'test_code_123',
    redirectUri: 'http://localhost:3001/',
    state: '4e6e928c1f6bddd0febfbdd75b8b2ce31298a66a7ba912a73766636473cc5bf7'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:48:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Invalid verification code format.',
    type: 'OAuthException',
    code: 100,
    fbtrace_id: 'AWM-jdpnZn1_XFYzFqA_T3e'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:49:16'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:49:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {
    code: 'test_code_123',
    redirectUri: 'http://localhost:3001/',
    state: '4e6e928c1f6bddd0febfbdd75b8b2ce31298a66a7ba912a73766636473cc5bf7'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:49:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'As3WkomV2z2n3mD3rvMkBOt'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:51:53'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:51:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQDf_anTrfJ-4V4vdvN7Sy3j2jbBpYrFCe3lHXeKvgEmxMLKAUuFNvUBROIkbxN6NiJUHn6-dhr9o80JcGzjW__o5bgG1PVCWGP6r2n13cTPwrpwMH1wIooaiocUYNydQzC5OfIo-KQqyy-EL998YYq4f2yvw7K3Wcj7CRZ9mpzWQ7RKd00NSapu82SUx2X-ZOUPWglB6MRbAl7eiQGJGSnABTLKk76BBhnMp0il5OLljlcO7WbLGUcXchGeTgnftKLVnL7_wTI91iQ2MRFscFZB3nTCtZ63JKO1ceKb3BApJvCuunUdTkVR4hWFzEPxaCgW1HASGucEPJMN8Feh5KyRsC2vwB9UVYpT7ht5iIiCdw0xflzO9w6h4i-VrZy5Y0LY4avRZKgb0Y4std5_YSjU',
    state: 'eb7f63c03e74fcd4d25b15d0fc387be6f3c16fa909959723a674ac210365304a',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:51:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'A2ly1oILOvKZrZTDe-tRx4-'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:53:52'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:53:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQCNb7Ly9KdC4Dz-6_sRm8Jtltqp69kx_SffQ1UW0cw2Re9V663WMVgoq7u_5PAdt_IReIifjL4zZyptszlwuXFvoKW6EuKgNJHkyjbwDCXFQFuIPJowLubT6xrWa-fFN-acM9EwQpLukdENAQDamfFNa6eE11rr6pE38sxY24SufFqKT-qFE5l9_Pqz093CQxEEQV1yzJRjPkMW2mpDqpaW961xLQl4Bd_ffQ9CyzfsJQeJrQVSwNx83heapN_o0csS89z1K6gC93L9VhN1ZOX-Xn3wTlioS3zHETS4J59JepERCLrGhobIIWMJ3Cq_g5th-4R-2Ja1INSf7dnZ8IDbD_tIa8OWzNNOJIxQZijCAlSkHQmSl6L2mkMdDJhwMM3I2xZOZbGKEAUm1PYTUa80',
    state: 'a79807eb02e8ef12b28d47df1c82a833849d9c3643fe5a26f61e5f809a6dc9e7',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:53:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'AnuMxIgUNQpZMRZ1apdsCGP'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:54:44'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:54:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQBD471rC-Q_vQgZon0QqY7Um1qZVRKUvcosrOKAVSo_v5yHuF6Qbx2cSNRAgKlxxp2QpgtbmyO4H4LIIV7ISTsicr13qqFCkdQaF18wirMGTBbk4w6APZYfv6nHT9Sjok_hU_PX7Ezb58JZ22CpllBDQNaikQ4Xo37BfGIeYye-KLvc9sCZ1jkP-A8H86RFqjRzHOCEUM06oCyfpcgOHdoI4ugYPop-siL3wn36QhqIYGink5Hr4SHCYc3V7covGPhzV9TIGe9bhPItZOhdBB8TlIAtdjfHeXnGPKnQHR4RCpC3SUxU4ogweFjsZIMco_1uBxXkUXOEWWkxUkzBAsHgLAmANDO1BMB27XRR5Ds2xTb5bOEaWTqYBuJeIr1EiryYsFVh_pMkRVdYLgYFfJMs',
    state: '46c7dab9bfba3fd288e436c842517344affcb839a74cf2ae3d8dc112c9207636',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:54:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'AHgQrHFaDYueLUSIReI21cF'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:54:54'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:54:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQAl223CJzMH9dwJLKeAMPETiLDykd0j-_dFnDU-jPh_zqPW71Fw2mbshXzXp21RMyUGlAs44yCRje7cuNI6pRtqOkWwDGFA3kQAwNT051Si_NEJeJM6I9XPT9y5fRYptfdLpTueMePPwj449GJXJ_yfoOowtg7XTadZL821KG00sglCD0UaRHgwnsDT_QwM-USVBSd4tDzkMJMi7vI8JssDOD9CkLsC9liai0d9m1DC68dGgfvFbZ1ZTWzHkOHYZndT2Jx-uCXmFNB8ylXZfGoB1HpD6lLtWhnDB3b3nG0Qw94yTDPukfK6jsqNAB-8sDyUjD11hXhEr0XcZH1ED1ZjfTENlxRl6GRD_MK5XCB-tbXkTUcEtPFWGzISir11xietdazE77Qaeh-yX4b6g83E',
    state: 'c193ed1817bde8eff210c63442b93ca59be425037bb921e510acc18c6585d0e2',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:54:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000,
  level: 'error',
  message: 'Uncaught Exception: listen EADDRINUSE: address already in use :::3000',
  stack: 'Error: listen EADDRINUSE: address already in use :::3000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n' +
    '    at listenInCluster (node:net:1994:12)\n' +
    '    at Server.listen (node:net:2099:7)\n' +
    '    at PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:288:17)',
  timestamp: '2025-06-25 18:56:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000,
  level: 'error',
  message: 'Uncaught Exception: listen EADDRINUSE: address already in use :::3000',
  stack: 'Error: listen EADDRINUSE: address already in use :::3000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n' +
    '    at listenInCluster (node:net:1994:12)\n' +
    '    at Server.listen (node:net:2099:7)\n' +
    '    at PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:288:17)',
  timestamp: '2025-06-25 19:01:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000,
  level: 'error',
  message: 'Uncaught Exception: listen EADDRINUSE: address already in use :::3000',
  stack: 'Error: listen EADDRINUSE: address already in use :::3000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n' +
    '    at listenInCluster (node:net:1994:12)\n' +
    '    at Server.listen (node:net:2099:7)\n' +
    '    at PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:288:17)',
  timestamp: '2025-06-25 19:01:21'
}
