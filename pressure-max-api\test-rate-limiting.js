const axios = require('axios');

async function testRateLimiting() {
  console.log('🧪 Testing Rate Limiting and Caching...\n');

  try {
    // Test 1: Multiple API calls to see rate limiting
    console.log('📊 Test 1: Rate Limiting - Making multiple API calls');
    const startTime = Date.now();
    
    console.log('⏱️ Call 1: Ad Accounts');
    await axios.get('http://localhost:3000/api/v1/facebook/ad-accounts');
    
    console.log('⏱️ Call 2: Campaigns (should be rate limited)');
    await axios.get('http://localhost:3000/api/v1/facebook/campaigns/act_263173616383414');
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    console.log(`✅ Total time for 2 API calls: ${totalTime}ms (should be ~2000ms due to rate limiting)\n`);

    // Test 2: Cache hit test
    console.log('💾 Test 2: Caching - Testing cache hits');
    const cacheStartTime = Date.now();
    
    console.log('📞 Making same campaign request again (should hit cache)');
    await axios.get('http://localhost:3000/api/v1/facebook/campaigns/act_263173616383414');
    
    const cacheEndTime = Date.now();
    const cacheTime = cacheEndTime - cacheStartTime;
    console.log(`✅ Cache response time: ${cacheTime}ms (should be <100ms)\n`);

    // Test 3: Campaign creation with cache invalidation
    console.log('🚀 Test 3: Campaign Creation with Cache Invalidation');
    try {
      const createResponse = await axios.post('http://localhost:3000/api/v1/facebook/campaigns', {
        adAccountId: 'act_263173616383414',
        name: 'Rate Limiting Test Campaign',
        objective: 'OUTCOME_TRAFFIC'
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Campaign creation response:', {
        success: createResponse.data.message ? true : false,
        campaignId: createResponse.data.campaign?.id || createResponse.data.campaign?.facebook_id,
        note: createResponse.data.error ? 'API Error - using fallback' : 'Real API success'
      });
    } catch (createError) {
      console.log('⚠️ Campaign creation failed (expected in some cases):', createError.response?.data?.error || createError.message);
    }

    console.log('\n📞 Making campaigns request after creation (cache should be cleared)');
    await axios.get('http://localhost:3000/api/v1/facebook/campaigns/act_263173616383414');

    // Test 4: Health check
    console.log('\n🏥 Test 4: Health Check');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ Health check:', {
      status: healthResponse.data.status,
      uptime: Math.round(healthResponse.data.uptime),
      version: healthResponse.data.version
    });

    console.log('\n🎉 Rate Limiting Tests Completed Successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Rate Limiting: 2-second delays between Facebook API calls');
    console.log('✅ Caching: 5-minute cache reduces redundant API calls');
    console.log('✅ Cache Invalidation: Cache cleared after campaign creation');
    console.log('✅ Error Handling: Graceful fallbacks when API fails');
    console.log('✅ Performance: Fast cache hits, controlled API usage');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

async function testConcurrentRequests() {
  console.log('\n🔄 Testing Concurrent Requests (Rate Limiting)...');
  
  try {
    const requests = [
      axios.get('http://localhost:3000/api/v1/facebook/ad-accounts'),
      axios.get('http://localhost:3000/api/v1/facebook/campaigns/act_263173616383414'),
      axios.get('http://localhost:3000/api/v1/facebook/campaigns/act_911817533003697')
    ];

    const startTime = Date.now();
    await Promise.all(requests);
    const endTime = Date.now();
    
    console.log(`✅ 3 concurrent requests completed in ${endTime - startTime}ms`);
    console.log('📝 Note: Rate limiting ensures proper spacing between Facebook API calls');
    
  } catch (error) {
    console.error('❌ Concurrent test failed:', error.message);
  }
}

async function runAllTests() {
  await testRateLimiting();
  await testConcurrentRequests();
  
  console.log('\n🏁 All Rate Limiting Tests Complete!');
}

runAllTests();
