{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:3000/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('accessToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle token refresh\napi.interceptors.response.use(response => response, async error => {\n  var _error$response;\n  const originalRequest = error.config;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    try {\n      const refreshToken = localStorage.getItem('refreshToken');\n      if (refreshToken) {\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refreshToken\n        });\n        const {\n          accessToken,\n          refreshToken: newRefreshToken\n        } = response.data.tokens;\n        localStorage.setItem('accessToken', accessToken);\n        localStorage.setItem('refreshToken', newRefreshToken);\n        originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n        return api(originalRequest);\n      }\n    } catch (refreshError) {\n      // Refresh failed, redirect to login\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      window.location.reload();\n    }\n  }\n  return Promise.reject(error);\n});\n\n// Auth API\nexport const authAPI = {\n  register: userData => api.post('/auth/register', userData),\n  login: credentials => api.post('/auth/login', credentials),\n  logout: () => api.post('/auth/logout'),\n  refresh: refreshToken => api.post('/auth/refresh', {\n    refreshToken\n  })\n};\n\n// User API\nexport const userAPI = {\n  getProfile: () => api.get('/users/profile'),\n  updateProfile: data => api.put('/users/profile', data),\n  getTeam: () => api.get('/users/team')\n};\n\n// Facebook API\nexport const facebookAPI = {\n  // Public endpoints (no authentication required)\n  getOAuthUrl: redirectUri => api.get('/facebook-public/oauth-url', {\n    params: {\n      redirectUri\n    }\n  }),\n  handleOAuthCallback: (code, state, redirectUri) => api.post('/facebook-public/oauth-callback', {\n    code,\n    state,\n    redirectUri\n  }),\n  // Authenticated endpoints (require user login)\n  getAdAccounts: () => api.get('/facebook/ad-accounts'),\n  getPages: () => api.get('/facebook/pages'),\n  createCampaign: campaignData => api.post('/facebook/campaigns', campaignData),\n  getCampaigns: adAccountId => api.get(`/facebook/campaigns/${adAccountId}`),\n  getCampaignInsights: (campaignId, dateRange) => api.get(`/facebook/campaigns/${campaignId}/insights`, {\n    params: dateRange\n  })\n};\n\n// Campaign API\nexport const campaignAPI = {\n  list: params => api.get('/campaigns', {\n    params\n  }),\n  create: data => api.post('/campaigns', data),\n  get: id => api.get(`/campaigns/${id}`),\n  update: (id, data) => api.put(`/campaigns/${id}`, data),\n  delete: id => api.delete(`/campaigns/${id}`)\n};\n\n// Health check\nexport const healthCheck = () => axios.get('http://localhost:3000/health');\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "accessToken", "newRefreshToken", "data", "tokens", "setItem", "refreshError", "removeItem", "window", "location", "reload", "authAPI", "register", "userData", "login", "credentials", "logout", "refresh", "userAPI", "getProfile", "get", "updateProfile", "put", "getTeam", "facebookAPI", "getOAuthUrl", "redirectUri", "params", "handleOAuthCallback", "code", "state", "getAdAccounts", "getPages", "createCampaign", "campaignData", "getCampaigns", "adAccountId", "getCampaignInsights", "campaignId", "date<PERSON><PERSON><PERSON>", "campaignAPI", "list", "id", "update", "delete", "healthCheck"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:3000/api/v1';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('accessToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle token refresh\napi.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (refreshToken) {\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n            refreshToken,\n          });\n\n          const { accessToken, refreshToken: newRefreshToken } = response.data.tokens;\n          localStorage.setItem('accessToken', accessToken);\n          localStorage.setItem('refreshToken', newRefreshToken);\n\n          originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n          return api(originalRequest);\n        }\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        window.location.reload();\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  register: (userData) => api.post('/auth/register', userData),\n  login: (credentials) => api.post('/auth/login', credentials),\n  logout: () => api.post('/auth/logout'),\n  refresh: (refreshToken) => api.post('/auth/refresh', { refreshToken }),\n};\n\n// User API\nexport const userAPI = {\n  getProfile: () => api.get('/users/profile'),\n  updateProfile: (data) => api.put('/users/profile', data),\n  getTeam: () => api.get('/users/team'),\n};\n\n// Facebook API\nexport const facebookAPI = {\n  // Public endpoints (no authentication required)\n  getOAuthUrl: (redirectUri) => api.get('/facebook-public/oauth-url', { params: { redirectUri } }),\n  handleOAuthCallback: (code, state, redirectUri) => api.post('/facebook-public/oauth-callback', { code, state, redirectUri }),\n\n  // Authenticated endpoints (require user login)\n  getAdAccounts: () => api.get('/facebook/ad-accounts'),\n  getPages: () => api.get('/facebook/pages'),\n  createCampaign: (campaignData) =>\n    api.post('/facebook/campaigns', campaignData),\n  getCampaigns: (adAccountId) => api.get(`/facebook/campaigns/${adAccountId}`),\n  getCampaignInsights: (campaignId, dateRange) => \n    api.get(`/facebook/campaigns/${campaignId}/insights`, { params: dateRange }),\n};\n\n// Campaign API\nexport const campaignAPI = {\n  list: (params) => api.get('/campaigns', { params }),\n  create: (data) => api.post('/campaigns', data),\n  get: (id) => api.get(`/campaigns/${id}`),\n  update: (id, data) => api.put(`/campaigns/${id}`, data),\n  delete: (id) => api.delete(`/campaigns/${id}`),\n};\n\n// Health check\nexport const healthCheck = () => axios.get('http://localhost:3000/health');\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,8BAA8B;;AAEnD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EACjD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACf,MAAMC,eAAe,GAAGL,KAAK,CAACL,MAAM;EAEpC,IAAI,EAAAS,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,IAAI;MACF,MAAMC,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACzD,IAAIU,YAAY,EAAE;QAChB,MAAML,QAAQ,GAAG,MAAMjB,KAAK,CAACuB,IAAI,CAAC,GAAGtB,YAAY,eAAe,EAAE;UAChEqB;QACF,CAAC,CAAC;QAEF,MAAM;UAAEE,WAAW;UAAEF,YAAY,EAAEG;QAAgB,CAAC,GAAGR,QAAQ,CAACS,IAAI,CAACC,MAAM;QAC3EhB,YAAY,CAACiB,OAAO,CAAC,aAAa,EAAEJ,WAAW,CAAC;QAChDb,YAAY,CAACiB,OAAO,CAAC,cAAc,EAAEH,eAAe,CAAC;QAErDN,eAAe,CAACd,OAAO,CAACQ,aAAa,GAAG,UAAUW,WAAW,EAAE;QAC/D,OAAOtB,GAAG,CAACiB,eAAe,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOU,YAAY,EAAE;MACrB;MACAlB,YAAY,CAACmB,UAAU,CAAC,aAAa,CAAC;MACtCnB,YAAY,CAACmB,UAAU,CAAC,cAAc,CAAC;MACvCC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B;EACF;EAEA,OAAOlB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMoB,OAAO,GAAG;EACrBC,QAAQ,EAAGC,QAAQ,IAAKlC,GAAG,CAACqB,IAAI,CAAC,gBAAgB,EAAEa,QAAQ,CAAC;EAC5DC,KAAK,EAAGC,WAAW,IAAKpC,GAAG,CAACqB,IAAI,CAAC,aAAa,EAAEe,WAAW,CAAC;EAC5DC,MAAM,EAAEA,CAAA,KAAMrC,GAAG,CAACqB,IAAI,CAAC,cAAc,CAAC;EACtCiB,OAAO,EAAGlB,YAAY,IAAKpB,GAAG,CAACqB,IAAI,CAAC,eAAe,EAAE;IAAED;EAAa,CAAC;AACvE,CAAC;;AAED;AACA,OAAO,MAAMmB,OAAO,GAAG;EACrBC,UAAU,EAAEA,CAAA,KAAMxC,GAAG,CAACyC,GAAG,CAAC,gBAAgB,CAAC;EAC3CC,aAAa,EAAGlB,IAAI,IAAKxB,GAAG,CAAC2C,GAAG,CAAC,gBAAgB,EAAEnB,IAAI,CAAC;EACxDoB,OAAO,EAAEA,CAAA,KAAM5C,GAAG,CAACyC,GAAG,CAAC,aAAa;AACtC,CAAC;;AAED;AACA,OAAO,MAAMI,WAAW,GAAG;EACzB;EACAC,WAAW,EAAGC,WAAW,IAAK/C,GAAG,CAACyC,GAAG,CAAC,4BAA4B,EAAE;IAAEO,MAAM,EAAE;MAAED;IAAY;EAAE,CAAC,CAAC;EAChGE,mBAAmB,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEJ,WAAW,KAAK/C,GAAG,CAACqB,IAAI,CAAC,iCAAiC,EAAE;IAAE6B,IAAI;IAAEC,KAAK;IAAEJ;EAAY,CAAC,CAAC;EAE5H;EACAK,aAAa,EAAEA,CAAA,KAAMpD,GAAG,CAACyC,GAAG,CAAC,uBAAuB,CAAC;EACrDY,QAAQ,EAAEA,CAAA,KAAMrD,GAAG,CAACyC,GAAG,CAAC,iBAAiB,CAAC;EAC1Ca,cAAc,EAAGC,YAAY,IAC3BvD,GAAG,CAACqB,IAAI,CAAC,qBAAqB,EAAEkC,YAAY,CAAC;EAC/CC,YAAY,EAAGC,WAAW,IAAKzD,GAAG,CAACyC,GAAG,CAAC,uBAAuBgB,WAAW,EAAE,CAAC;EAC5EC,mBAAmB,EAAEA,CAACC,UAAU,EAAEC,SAAS,KACzC5D,GAAG,CAACyC,GAAG,CAAC,uBAAuBkB,UAAU,WAAW,EAAE;IAAEX,MAAM,EAAEY;EAAU,CAAC;AAC/E,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,IAAI,EAAGd,MAAM,IAAKhD,GAAG,CAACyC,GAAG,CAAC,YAAY,EAAE;IAAEO;EAAO,CAAC,CAAC;EACnD/C,MAAM,EAAGuB,IAAI,IAAKxB,GAAG,CAACqB,IAAI,CAAC,YAAY,EAAEG,IAAI,CAAC;EAC9CiB,GAAG,EAAGsB,EAAE,IAAK/D,GAAG,CAACyC,GAAG,CAAC,cAAcsB,EAAE,EAAE,CAAC;EACxCC,MAAM,EAAEA,CAACD,EAAE,EAAEvC,IAAI,KAAKxB,GAAG,CAAC2C,GAAG,CAAC,cAAcoB,EAAE,EAAE,EAAEvC,IAAI,CAAC;EACvDyC,MAAM,EAAGF,EAAE,IAAK/D,GAAG,CAACiE,MAAM,CAAC,cAAcF,EAAE,EAAE;AAC/C,CAAC;;AAED;AACA,OAAO,MAAMG,WAAW,GAAGA,CAAA,KAAMpE,KAAK,CAAC2C,GAAG,CAAC,8BAA8B,CAAC;AAE1E,eAAezC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}