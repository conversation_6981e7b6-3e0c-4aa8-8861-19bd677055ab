[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11"}, {"size": 232, "mtime": 1750870627179, "results": "12", "hashOfConfig": "13"}, {"size": 2140, "mtime": 1750906675864, "results": "14", "hashOfConfig": "13"}, {"size": 10186, "mtime": 1750904956981, "results": "15", "hashOfConfig": "13"}, {"size": 13240, "mtime": 1750911536379, "results": "16", "hashOfConfig": "13"}, {"size": 7324, "mtime": 1750904956983, "results": "17", "hashOfConfig": "13"}, {"size": 8243, "mtime": 1750873838649, "results": "18", "hashOfConfig": "13"}, {"size": 3438, "mtime": 1750870723227, "results": "19", "hashOfConfig": "13"}, {"size": 4079, "mtime": 1750906474408, "results": "20", "hashOfConfig": "13"}, {"size": 4531, "mtime": 1750906642416, "results": "21", "hashOfConfig": "13"}, {"size": 4881, "mtime": 1750906513515, "results": "22", "hashOfConfig": "13"}, {"size": 9011, "mtime": 1750911462213, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["57"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["58", "59"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["60"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["61", "62"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["63"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 47, "column": 6, "nodeType": "66", "endLine": 47, "endColumn": 23, "suggestions": "67"}, {"ruleId": "68", "severity": 1, "message": "69", "line": 8, "column": 28, "nodeType": "70", "messageId": "71", "endLine": 8, "endColumn": 32}, {"ruleId": "64", "severity": 1, "message": "72", "line": 22, "column": 6, "nodeType": "66", "endLine": 22, "endColumn": 23, "suggestions": "73"}, {"ruleId": "64", "severity": 1, "message": "74", "line": 22, "column": 6, "nodeType": "66", "endLine": 22, "endColumn": 8, "suggestions": "75"}, {"ruleId": "68", "severity": 1, "message": "76", "line": 5, "column": 20, "nodeType": "70", "messageId": "71", "endLine": 5, "endColumn": 25}, {"ruleId": "64", "severity": 1, "message": "77", "line": 25, "column": 6, "nodeType": "66", "endLine": 25, "endColumn": 32, "suggestions": "78"}, {"ruleId": "64", "severity": 1, "message": "79", "line": 24, "column": 6, "nodeType": "66", "endLine": 24, "endColumn": 8, "suggestions": "80"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadAdSets', 'loadAds', and 'loadCampaigns'. Either include them or remove the dependency array.", "ArrayExpression", ["81"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["82"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["83"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["84"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["85"], {"desc": "86", "fix": "87"}, {"desc": "88", "fix": "89"}, {"desc": "90", "fix": "91"}, {"desc": "92", "fix": "93"}, {"desc": "94", "fix": "95"}, "Update the dependencies array to be: [loadAdSets, loadAds, loadCampaigns, selectedAccount]", {"range": "96", "text": "97"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "98", "text": "99"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "100", "text": "101"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "102", "text": "103"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "104", "text": "105"}, [1688, 1705], "[loadAdSets, loadAds, loadCampaigns, selectedAccount]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]"]