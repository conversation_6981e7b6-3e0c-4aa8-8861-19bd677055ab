[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12"}, {"size": 232, "mtime": 1750870627179, "results": "13", "hashOfConfig": "14"}, {"size": 2140, "mtime": 1750906675864, "results": "15", "hashOfConfig": "14"}, {"size": 10186, "mtime": 1750904956981, "results": "16", "hashOfConfig": "14"}, {"size": 15049, "mtime": 1750916438402, "results": "17", "hashOfConfig": "14"}, {"size": 7324, "mtime": 1750904956983, "results": "18", "hashOfConfig": "14"}, {"size": 8243, "mtime": 1750873838649, "results": "19", "hashOfConfig": "14"}, {"size": 3438, "mtime": 1750870723227, "results": "20", "hashOfConfig": "14"}, {"size": 4079, "mtime": 1750906474408, "results": "21", "hashOfConfig": "14"}, {"size": 4531, "mtime": 1750906642416, "results": "22", "hashOfConfig": "14"}, {"size": 4881, "mtime": 1750906513515, "results": "23", "hashOfConfig": "14"}, {"size": 15521, "mtime": 1750913282515, "results": "24", "hashOfConfig": "14"}, {"size": 11191, "mtime": 1750915070114, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["62", "63"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["64", "65"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["66"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["67", "68"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["69"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 58, "column": 6, "nodeType": "72", "endLine": 58, "endColumn": 23, "suggestions": "73"}, {"ruleId": "70", "severity": 1, "message": "74", "line": 83, "column": 6, "nodeType": "72", "endLine": 83, "endColumn": 46, "suggestions": "75"}, {"ruleId": "76", "severity": 1, "message": "77", "line": 8, "column": 28, "nodeType": "78", "messageId": "79", "endLine": 8, "endColumn": 32}, {"ruleId": "70", "severity": 1, "message": "80", "line": 22, "column": 6, "nodeType": "72", "endLine": 22, "endColumn": 23, "suggestions": "81"}, {"ruleId": "70", "severity": 1, "message": "82", "line": 22, "column": 6, "nodeType": "72", "endLine": 22, "endColumn": 8, "suggestions": "83"}, {"ruleId": "76", "severity": 1, "message": "84", "line": 5, "column": 20, "nodeType": "78", "messageId": "79", "endLine": 5, "endColumn": 25}, {"ruleId": "70", "severity": 1, "message": "85", "line": 25, "column": 6, "nodeType": "72", "endLine": 25, "endColumn": 32, "suggestions": "86"}, {"ruleId": "70", "severity": 1, "message": "87", "line": 24, "column": 6, "nodeType": "72", "endLine": 24, "endColumn": 8, "suggestions": "88"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["89"], "React Hook useEffect has missing dependencies: 'loadAdSets', 'loadAds', and 'loadCampaigns'. Either include them or remove the dependency array.", ["90"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["91"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["92"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["93"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["94"], {"desc": "95", "fix": "96"}, {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, {"desc": "101", "fix": "102"}, {"desc": "103", "fix": "104"}, {"desc": "105", "fix": "106"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "107", "text": "108"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns, loadAdSets, loadAds]", {"range": "109", "text": "110"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "111", "text": "112"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "113", "text": "114"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "117", "text": "118"}, [2024, 2041], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2588, 2628], "[activeTab, selectedAccount, loadedData, loadCampaigns, loadAdSets, loadAds]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]"]