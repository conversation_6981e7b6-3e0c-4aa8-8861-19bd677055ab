{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:16:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:21'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:21'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:23'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:23'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:26'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:26'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:27'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:27'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:31'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:43'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:43'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:52'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:24:53'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Redis connection failed, continuing without Redis:',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:14'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:24'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:24'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Redis connection failed, continuing without Redis:',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:39'
}
{
  message: '::1 - - [26/Jun/2025:00:25:58 +0000] "GET /health HTTP/1.1" 200 97 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:25:58'
}
{
  message: '::1 - - [26/Jun/2025:00:26:05 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http://localhost:3001 HTTP/1.1" 401 83 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:26:05'
}
{
  message: '::1 - - [26/Jun/2025:00:26:06 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:26:06'
}
{
  message: '::1 - - [26/Jun/2025:00:26:06 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:26:06'
}
{
  message: '::1 - - [26/Jun/2025:00:26:06 +0000] "GET /favicon.ico HTTP/1.1" 404 90 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:26:06'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Client error:',
  timestamp: '2025-06-25 17:26:39'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:27:25'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:27:25'
}
{
  message: '::1 - - [26/Jun/2025:00:27:37 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:27:37'
}
{
  message: '::1 - - [26/Jun/2025:00:27:37 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:27:37'
}
{
  message: '::1 - - [26/Jun/2025:00:27:42 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:27:42'
}
{
  message: '::1 - - [26/Jun/2025:00:27:45 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:27:45'
}
{
  message: '::1 - - [26/Jun/2025:00:28:06 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:28:06'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:28:53'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:28:53'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:09'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:09'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:38'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:39'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:39'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:48'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:29:49'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:00'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:01'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Redis connection failed, continuing without Redis:',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:16'
}
{
  message: '::1 - - [26/Jun/2025:00:30:25 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:25'
}
{
  message: '::1 - - [26/Jun/2025:00:30:25 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:25'
}
{
  message: '::1 - - [26/Jun/2025:00:30:27 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:27'
}
{
  message: '::1 - - [26/Jun/2025:00:30:27 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:27'
}
{
  message: '::1 - - [26/Jun/2025:00:30:34 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:34'
}
{
  message: '::1 - - [26/Jun/2025:00:30:37 +0000] "GET /api/v1/facebook-public/test HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:37'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:46:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:30:43'
}
{
  message: '::1 - - [26/Jun/2025:00:30:43 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001 HTTP/1.1" 400 459 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:46:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http%3A%2F%2Flocalhost%3A3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:30:50'
}
{
  message: '::1 - - [26/Jun/2025:00:30:50 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http%3A%2F%2Flocalhost%3A3001 HTTP/1.1" 400 459 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:30:50'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Redis connection failed, continuing without Redis:',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:16'
}
{
  message: '::1 - - [26/Jun/2025:00:31:26 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:26'
}
{
  message: '::1 - - [26/Jun/2025:00:31:27 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:27'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:51:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:31:46'
}
{
  message: '::1 - - [26/Jun/2025:00:31:46 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001 HTTP/1.1" 400 459 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:46'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:59'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  message: 'Redis max retries reached, giving up',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Redis connection failed, continuing without Redis:',
  timestamp: '2025-06-25 17:32:00'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:00'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:00'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:00'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:00'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001',
  state: 'edf22066...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 17:32:14'
}
{
  message: '::1 - - [26/Jun/2025:00:32:14 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001 HTTP/1.1" 200 467 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:14'
}
{
  message: '::1 - - [26/Jun/2025:00:32:22 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:22'
}
{
  message: '::1 - - [26/Jun/2025:00:32:22 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:22'
}
{
  message: '::1 - - [26/Jun/2025:00:32:24 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:24'
}
{
  message: '::1 - - [26/Jun/2025:00:32:24 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:24'
}
{
  message: '::1 - - [26/Jun/2025:00:32:43 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:43'
}
{
  message: '::1 - - [26/Jun/2025:00:32:43 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:43'
}
{
  message: '::1 - - [26/Jun/2025:00:32:45 +0000] "GET / HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:45'
}
{
  message: '::1 - - [26/Jun/2025:00:32:45 +0000] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 127 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:32:45'
}
{
  message: '::1 - - [26/Jun/2025:00:33:22 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:22'
}
{
  message: '::1 - - [26/Jun/2025:00:33:22 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:22'
}
{
  message: '::1 - - [26/Jun/2025:00:33:26 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:26'
}
{
  message: '::1 - - [26/Jun/2025:00:33:28 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Client error:',
  timestamp: '2025-06-25 17:33:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'd1cf2da5...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 17:33:31'
}
{
  message: '::1 - - [26/Jun/2025:00:33:31 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:31'
}
{
  message: '::1 - - [26/Jun/2025:00:33:34 +0000] "GET /health HTTP/1.1" 200 96 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:34'
}
{
  message: '::1 - - [26/Jun/2025:00:33:34 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:34'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '7725da2d...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 17:33:37'
}
{
  message: '::1 - - [26/Jun/2025:00:33:37 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:37'
}
{
  message: '::1 - - [26/Jun/2025:00:33:45 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:45'
}
{
  message: '::1 - - [26/Jun/2025:00:33:45 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:115:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQAS_iV7NOJo_tCiaowxNmmN0pSGFlL0SvntCWC-96w_XgXoVitw7EnqlUX3FfYeK04n9rX-sQ6LLAEw_pYpTzj8fC5gE82fVoSdZW_CafbxxXK6QsXz-lUDFM8K1JHB8w0Shhj7ssNKmNQHJ6KK5XYDD1yCzAOK1AZReuJv8TvRWhtZpyjZsGMwuT6pkBlDmzfTKmVG1XNJKVqqJbNV9WPNq7PWf2pQA30ftXbKkhmrgjzVj-HMWJWiI9XUPQIGHTze1j73HMenS8VCxHtL82fU0WP688UVXDOk-ds82InlUuaXfyhz__JPtxJOm7mscuwTCMtIfCQUrhV9t5OsTR7Klt0QgrsZTynsif9qtL1N_7MFkixVCGsXPCro44gkRwOzjEQC4okWzzTUUwNnmVlg',
    state: '7725da2de3ac835210975689b0561035aa60f628b4347a5bce3d5300018c12bb',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 17:33:45'
}
{
  message: '::1 - - [26/Jun/2025:00:33:45 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 400 555 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:33:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001',
  state: '3ce27204...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 17:34:03'
}
{
  message: '::1 - - [26/Jun/2025:00:34:03 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001 HTTP/1.1" 200 467 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:34:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'warn',
  message: 'Client error:',
  timestamp: '2025-06-25 17:34:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '791016d9...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 17:34:15'
}
{
  message: '::1 - - [26/Jun/2025:00:34:15 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:34:15'
}
{
  message: '::1 - - [26/Jun/2025:00:34:22 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:34:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:115:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQDkU2DQi8yL0lp-8P12S7TCoM15XG7qwDRh_AvEM7cNEf8G0nbYvVRC0EjReMK6FugdNnOMafjaZ49J5jd80afcxNBMlL2tS-e_z6K5QJOB3ikJEFZ8N06GhUVZa0FvmhoGddH16r2xDyCGZZlp7O0naMeIsvWA_zC3685eb8BeOSOq0_t1rSOibtqtCmlvyFhKqJjuwC5niqntG-T_x9MCcV_NcXZqIDGYmhXumcHBvY6z2hKrYcVbTnqK2xIxeiOjI9Qq2vR00Tfda_Nga8ldH0G8ctsZ0OiVmWz1Q_El0f3O1UCnyK8ipJQpRGhUwFCRvu7ntrVnRtLv_9CWq1cuxvXxyIkkdppZDBziGGftfpxZHAmt-oTnLe3diWOgMjFmceUJv0WhqQYwO9OjpB3R',
    state: '791016d9ee0e769ce38730835e116229f8ae58455ff9ef1b726116d055460921',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 17:34:22'
}
{
  message: '::1 - - [26/Jun/2025:00:34:22 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 400 555 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:34:22'
}
{
  message: '::1 - - [26/Jun/2025:00:34:22 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 17:34:22'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:17:20'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:18:28'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:32'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:32'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:34'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:34'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:35'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:35'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:37'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:37'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:40'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:42'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:42'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:47'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:47'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:51'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:51'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:55'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:55'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:56'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:56'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:57'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:57'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:58'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:02'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:02'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:04'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:04'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:05'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:05'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:06'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:06'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:07'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:07'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:08'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:08'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:10'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:10'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:11'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:11'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:12'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:12'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:13'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:13'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:19'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:19:50'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:22:20'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:11'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:29'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:24:40'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:06'
}
{
  message: '::1 - - [26/Jun/2025:01:25:24 +0000] "GET /health HTTP/1.1" 200 97 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:24'
}
{
  message: '::1 - - [26/Jun/2025:01:25:59 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:59'
}
{
  message: '::1 - - [26/Jun/2025:01:25:59 +0000] "GET /health HTTP/1.1" 200 96 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:25:59'
}
{
  message: '::1 - - [26/Jun/2025:01:26:04 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:26:04'
}
{
  message: '::1 - - [26/Jun/2025:01:26:36 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:26:36'
}
{
  message: '::1 - - [26/Jun/2025:01:26:36 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:26:36'
}
{
  message: '::1 - - [26/Jun/2025:01:26:38 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:26:38'
}
{
  message: '::1 - - [26/Jun/2025:01:28:34 +0000] "GET /api-docs HTTP/1.1" 404 87 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:28:34'
}
{
  message: '::1 - - [26/Jun/2025:01:28:52 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:28:52'
}
{
  message: '::1 - - [26/Jun/2025:01:34:14 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:34:14'
}
{
  message: '::1 - - [26/Jun/2025:01:34:16 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:34:16'
}
{
  message: '::1 - - [26/Jun/2025:01:34:16 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:34:16'
}
{
  message: '::1 - - [26/Jun/2025:01:34:18 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:34:18'
}
{
  message: '::1 - - [26/Jun/2025:01:34:19 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:34:19'
}
{
  message: '::1 - - [26/Jun/2025:01:34:20 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:34:20'
}
{
  message: '::1 - - [26/Jun/2025:01:34:20 +0000] "GET /api/v1/facebook/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 401 83 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:34:20'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:36'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:36:45'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '75622863...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:37:56'
}
{
  message: '::1 - - [26/Jun/2025:01:37:56 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001/ HTTP/1.1" 200 470 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:37:56'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '5ffaf235...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:38:20'
}
{
  message: '::1 - - [26/Jun/2025:01:38:20 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001/ HTTP/1.1" 200 470 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:38:20'
}
{
  message: '::1 - - [26/Jun/2025:01:39:10 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:10'
}
{
  message: '::1 - - [26/Jun/2025:01:39:10 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:10'
}
{
  message: '::1 - - [26/Jun/2025:01:39:10 +0000] "GET /favicon.ico HTTP/1.1" 404 90 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:10'
}
{
  message: '::1 - - [26/Jun/2025:01:39:23 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:23'
}
{
  message: '::1 - - [26/Jun/2025:01:39:23 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:23'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '6e1535b4...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:39:27'
}
{
  message: '::1 - - [26/Jun/2025:01:39:27 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:27'
}
{
  message: '::1 - - [26/Jun/2025:01:39:33 +0000] "GET /main.9f3ba93506331e70fed9.hot-update.json HTTP/1.1" 404 120 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:33'
}
{
  message: '::1 - - [26/Jun/2025:01:39:34 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:34'
}
{
  message: '::1 - - [26/Jun/2025:01:39:34 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:39:34'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:40:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  userId: '****************',
  userName: 'Cody Houser',
  adAccountsCount: 6,
  level: 'info',
  message: 'Facebook token test successful',
  timestamp: '2025-06-25 18:41:10'
}
{
  message: '::1 - - [26/Jun/2025:01:41:10 +0000] "POST /api/v1/facebook-public/test-token HTTP/1.1" 200 1625 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:41:10'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  userId: '****************',
  userName: 'Cody Houser',
  adAccountsCount: 6,
  level: 'info',
  message: 'Facebook token test successful',
  timestamp: '2025-06-25 18:41:20'
}
{
  message: '::1 - - [26/Jun/2025:01:41:20 +0000] "POST /api/v1/facebook-public/test-token HTTP/1.1" 200 1625 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:41:20'
}
{
  message: '::1 - - [26/Jun/2025:01:45:25 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:45:25'
}
{
  message: '::1 - - [26/Jun/2025:01:45:25 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:45:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'e38f33e2...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:45:29'
}
{
  message: '::1 - - [26/Jun/2025:01:45:29 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:45:29'
}
{
  message: '::1 - - [26/Jun/2025:01:45:36 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:45:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:116:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQDBUrkjEzpaZ0vUl8zfT1XOVa_3CUe0sjRFXvBmvtFp-BTt3qVeOiU2SvA74Rj6NJ4bq2KoDsiT03InhhpMj1I18sXuAxykU8VHXzV2I-Ly4PbWThi4U8uaagr7jzAqCmqy48atpTfEUi8-iWbJOgZDD4kCTyEW4w8boTt_PGg5nJiWwIE_7369rT9SBLfSyKF_WA01JXhe5NZqxa2wjKroww_3_2LLzssmQ20NKnJnJMXvqRZCCjrt49M3tTUCxrevqiqcB9j5pkKWudp8ew0OejryxBGJcVExrYRpaf1LufIP0YeFEFnFboWa0lgFEpgjiSl8SqLuSDh9mDEs8v_B91jLI0_8P49M1AmjVQQITldyf5Hfdx9gJ0NjPJnhs-QZo8VTJrOUOH5nwesk5Zjj',
    state: 'e38f33e22830529f74eeab5b276a29e14f002b8f95cb59140971f19870089201',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:45:36'
}
{
  message: '::1 - - [26/Jun/2025:01:45:36 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 400 459 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:45:36'
}
{
  message: '::1 - - [26/Jun/2025:01:45:36 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:45:36'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:46:26'
}
{
  message: '::1 - - [26/Jun/2025:01:48:15 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:15'
}
{
  message: '::1 - - [26/Jun/2025:01:48:15 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:15'
}
{
  message: '::1 - - [26/Jun/2025:01:48:22 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:22'
}
{
  message: '::1 - - [26/Jun/2025:01:48:22 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:22'
}
{
  message: '::1 - - [26/Jun/2025:01:48:28 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:28'
}
{
  message: '::1 - - [26/Jun/2025:01:48:28 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:28'
}
{
  message: '::1 - - [26/Jun/2025:01:48:31 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:31'
}
{
  message: '::1 - - [26/Jun/2025:01:48:31 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '5e185b7e...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:48:33'
}
{
  message: '::1 - - [26/Jun/2025:01:48:33 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '4e6e928c...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:48:39'
}
{
  message: '::1 - - [26/Jun/2025:01:48:39 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001/ HTTP/1.1" 200 470 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:39'
}
{
  message: '::1 - - [26/Jun/2025:01:48:41 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:116:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQCPOhA8gu_deRTB7v5T7k7PvYgGUIdmdH2PnjW5yiTW0PNMZjOVpZQfE2gcymygsvVJWQeYQ6DIq2z5dzWPEswy0XaC8rT23sW2eiNNerMZVCNNvTO8X1fqWKk_rbhctvJICgG4bIZcAxcKB_oQCxHvxLeYKviYOyUiGMs_dRS7yTa7_EGfG9vUNdzW6nvUNP-UjI4R_INpgnkH09leiuDdHNzJ9U8NEby07JLEG4B0UK4DsxtbA6ZoKHMFNMqp_D_lIizgwBg3MHYUVdJjw0bA7K1sdYh8tcRPnM3Hja4Z39bnpcLm5CxBAh4QVowbdNVuNNNNhaTMvWMrSSEmjnfj8HHDPMtWYuZ63rduUnjbcB_IbjvSZsXhcvkZiPDoSGN7SmfSB5Rfm8z1S6OnkLIv',
    state: '5e185b7efcfcb79bdee2a3ca3c0d9639eb032d2b396c405452cc14aa52c57a27',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:48:41'
}
{
  message: '::1 - - [26/Jun/2025:01:48:41 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 400 459 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:41'
}
{
  message: '::1 - - [26/Jun/2025:01:48:41 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:116:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {
    code: 'test_code_123',
    redirectUri: 'http://localhost:3001/',
    state: '4e6e928c1f6bddd0febfbdd75b8b2ce31298a66a7ba912a73766636473cc5bf7'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:48:48'
}
{
  message: '::1 - - [26/Jun/2025:01:48:48 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 400 459 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:48:48'
}
{
  message: '::1 - - [26/Jun/2025:01:49:00 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:00'
}
{
  message: '::1 - - [26/Jun/2025:01:49:00 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:00'
}
{
  message: '::1 - - [26/Jun/2025:01:49:00 +0000] "GET /favicon.ico HTTP/1.1" 404 90 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:00'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:04'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'd9da18ff...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:49:05'
}
{
  message: '::1 - - [26/Jun/2025:01:49:05 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:05'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Invalid verification code format.',
    type: 'OAuthException',
    code: 100,
    fbtrace_id: 'AWM-jdpnZn1_XFYzFqA_T3e'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:49:16'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:49:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {
    code: 'test_code_123',
    redirectUri: 'http://localhost:3001/',
    state: '4e6e928c1f6bddd0febfbdd75b8b2ce31298a66a7ba912a73766636473cc5bf7'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:49:16'
}
{
  message: '::1 - - [26/Jun/2025:01:49:16 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 502 297 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:49:16'
}
{
  message: '::1 - - [26/Jun/2025:01:51:41 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:41'
}
{
  message: '::1 - - [26/Jun/2025:01:51:41 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:41'
}
{
  message: '::1 - - [26/Jun/2025:01:51:41 +0000] "GET /favicon.ico HTTP/1.1" 404 90 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'eb7f63c0...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:51:45'
}
{
  message: '::1 - - [26/Jun/2025:01:51:45 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:45'
}
{
  message: '::1 - - [26/Jun/2025:01:51:52 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:52'
}
{
  message: '::1 - - [26/Jun/2025:01:51:52 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:52'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'As3WkomV2z2n3mD3rvMkBOt'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:51:53'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:51:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQDf_anTrfJ-4V4vdvN7Sy3j2jbBpYrFCe3lHXeKvgEmxMLKAUuFNvUBROIkbxN6NiJUHn6-dhr9o80JcGzjW__o5bgG1PVCWGP6r2n13cTPwrpwMH1wIooaiocUYNydQzC5OfIo-KQqyy-EL998YYq4f2yvw7K3Wcj7CRZ9mpzWQ7RKd00NSapu82SUx2X-ZOUPWglB6MRbAl7eiQGJGSnABTLKk76BBhnMp0il5OLljlcO7WbLGUcXchGeTgnftKLVnL7_wTI91iQ2MRFscFZB3nTCtZ63JKO1ceKb3BApJvCuunUdTkVR4hWFzEPxaCgW1HASGucEPJMN8Feh5KyRsC2vwB9UVYpT7ht5iIiCdw0xflzO9w6h4i-VrZy5Y0LY4avRZKgb0Y4std5_YSjU',
    state: 'eb7f63c03e74fcd4d25b15d0fc387be6f3c16fa909959723a674ac210365304a',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:51:53'
}
{
  message: '::1 - - [26/Jun/2025:01:51:53 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 502 297 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:51:53'
}
{
  message: '::1 - - [26/Jun/2025:01:53:20 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:20'
}
{
  message: '::1 - - [26/Jun/2025:01:53:20 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:20'
}
{
  message: '::1 - - [26/Jun/2025:01:53:35 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:35'
}
{
  message: '::1 - - [26/Jun/2025:01:53:35 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:35'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'a79807eb...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:53:47'
}
{
  message: '::1 - - [26/Jun/2025:01:53:47 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:47'
}
{
  message: '::1 - - [26/Jun/2025:01:53:52 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:52'
}
{
  message: '::1 - - [26/Jun/2025:01:53:52 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:52'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'A2ly1oILOvKZrZTDe-tRx4-'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:53:52'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:53:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQCNb7Ly9KdC4Dz-6_sRm8Jtltqp69kx_SffQ1UW0cw2Re9V663WMVgoq7u_5PAdt_IReIifjL4zZyptszlwuXFvoKW6EuKgNJHkyjbwDCXFQFuIPJowLubT6xrWa-fFN-acM9EwQpLukdENAQDamfFNa6eE11rr6pE38sxY24SufFqKT-qFE5l9_Pqz093CQxEEQV1yzJRjPkMW2mpDqpaW961xLQl4Bd_ffQ9CyzfsJQeJrQVSwNx83heapN_o0csS89z1K6gC93L9VhN1ZOX-Xn3wTlioS3zHETS4J59JepERCLrGhobIIWMJ3Cq_g5th-4R-2Ja1INSf7dnZ8IDbD_tIa8OWzNNOJIxQZijCAlSkHQmSl6L2mkMdDJhwMM3I2xZOZbGKEAUm1PYTUa80',
    state: 'a79807eb02e8ef12b28d47df1c82a833849d9c3643fe5a26f61e5f809a6dc9e7',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:53:52'
}
{
  message: '::1 - - [26/Jun/2025:01:53:52 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 502 297 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:53:52'
}
{
  message: '::1 - - [26/Jun/2025:01:54:33 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:33'
}
{
  message: '::1 - - [26/Jun/2025:01:54:33 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '46c7dab9...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:54:38'
}
{
  message: '::1 - - [26/Jun/2025:01:54:38 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:38'
}
{
  message: '::1 - - [26/Jun/2025:01:54:43 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:43'
}
{
  message: '::1 - - [26/Jun/2025:01:54:43 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:43'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'AnuMxIgUNQpZMRZ1apdsCGP'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:54:44'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:54:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQBD471rC-Q_vQgZon0QqY7Um1qZVRKUvcosrOKAVSo_v5yHuF6Qbx2cSNRAgKlxxp2QpgtbmyO4H4LIIV7ISTsicr13qqFCkdQaF18wirMGTBbk4w6APZYfv6nHT9Sjok_hU_PX7Ezb58JZ22CpllBDQNaikQ4Xo37BfGIeYye-KLvc9sCZ1jkP-A8H86RFqjRzHOCEUM06oCyfpcgOHdoI4ugYPop-siL3wn36QhqIYGink5Hr4SHCYc3V7covGPhzV9TIGe9bhPItZOhdBB8TlIAtdjfHeXnGPKnQHR4RCpC3SUxU4ogweFjsZIMco_1uBxXkUXOEWWkxUkzBAsHgLAmANDO1BMB27XRR5Ds2xTb5bOEaWTqYBuJeIr1EiryYsFVh_pMkRVdYLgYFfJMs',
    state: '46c7dab9bfba3fd288e436c842517344affcb839a74cf2ae3d8dc112c9207636',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:54:44'
}
{
  message: '::1 - - [26/Jun/2025:01:54:44 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 502 297 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:44'
}
{
  message: '::1 - - [26/Jun/2025:01:54:46 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:46'
}
{
  message: '::1 - - [26/Jun/2025:01:54:46 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'c193ed18...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:54:48'
}
{
  message: '::1 - - [26/Jun/2025:01:54:48 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:48'
}
{
  message: '::1 - - [26/Jun/2025:01:54:54 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:54'
}
{
  message: '::1 - - [26/Jun/2025:01:54:54 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:54'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  error: {
    message: 'Error validating client secret.',
    type: 'OAuthException',
    code: 1,
    fbtrace_id: 'AHgQrHFaDYueLUSIReI21cF'
  },
  level: 'error',
  message: 'Facebook token exchange error:',
  timestamp: '2025-06-25 18:54:54'
}
{
  service: 'Facebook',
  environment: 'development',
  statusCode: 502,
  isOperational: true,
  status: 'error',
  type: 'external_service',
  level: 'error',
  message: 'Facebook OAuth callback error: Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  timestamp: '2025-06-25 18:54:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Failed to exchange authorization code for token',
  stack: 'Error: Failed to exchange authorization code for token\n' +
    '    at FacebookService.exchangeCodeForToken (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\services\\facebookService.js:64:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:153:23',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQAl223CJzMH9dwJLKeAMPETiLDykd0j-_dFnDU-jPh_zqPW71Fw2mbshXzXp21RMyUGlAs44yCRje7cuNI6pRtqOkWwDGFA3kQAwNT051Si_NEJeJM6I9XPT9y5fRYptfdLpTueMePPwj449GJXJ_yfoOowtg7XTadZL821KG00sglCD0UaRHgwnsDT_QwM-USVBSd4tDzkMJMi7vI8JssDOD9CkLsC9liai0d9m1DC68dGgfvFbZ1ZTWzHkOHYZndT2Jx-uCXmFNB8ylXZfGoB1HpD6lLtWhnDB3b3nG0Qw94yTDPukfK6jsqNAB-8sDyUjD11hXhEr0XcZH1ED1ZjfTENlxRl6GRD_MK5XCB-tbXkTUcEtPFWGzISir11xietdazE77Qaeh-yX4b6g83E',
    state: 'c193ed1817bde8eff210c63442b93ca59be425037bb921e510acc18c6585d0e2',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 18:54:54'
}
{
  message: '::1 - - [26/Jun/2025:01:54:54 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 502 297 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:54:54'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:55:42'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:20'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '9472e399...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:56:46'
}
{
  message: '::1 - - [26/Jun/2025:01:56:47 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:47'
}
{
  message: '::1 - - [26/Jun/2025:01:56:58 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:58'
}
{
  message: '::1 - - [26/Jun/2025:01:56:58 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:58'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:58'
}
{
  message: 'Using demo access token for development',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:58'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  facebookUserId: '****************',
  permissions: 0,
  level: 'info',
  message: 'Facebook OAuth completed successfully',
  timestamp: '2025-06-25 18:56:58'
}
{
  message: '::1 - - [26/Jun/2025:01:56:58 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 200 630 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:56:58'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:05'
}
{
  message: 'Using demo access token for development',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:05'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  facebookUserId: '****************',
  permissions: 0,
  level: 'info',
  message: 'Facebook OAuth completed successfully',
  timestamp: '2025-06-25 18:57:05'
}
{
  message: '::1 - - [26/Jun/2025:01:57:05 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 200 630 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:05'
}
{
  message: '::1 - - [26/Jun/2025:01:57:14 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:14'
}
{
  message: '::1 - - [26/Jun/2025:01:57:14 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'b1e85fbd...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 18:57:25'
}
{
  message: '::1 - - [26/Jun/2025:01:57:25 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:25'
}
{
  message: '::1 - - [26/Jun/2025:01:57:30 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:30'
}
{
  message: '::1 - - [26/Jun/2025:01:57:30 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:30'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:30'
}
{
  message: 'Using demo access token for development',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  facebookUserId: '****************',
  permissions: 0,
  level: 'info',
  message: 'Facebook OAuth completed successfully',
  timestamp: '2025-06-25 18:57:30'
}
{
  message: '::1 - - [26/Jun/2025:01:57:30 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 200 630 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 18:57:30'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:01:03'
}
{
  message: '::1 - - [26/Jun/2025:02:01:06 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:01:06'
}
{
  message: '::1 - - [26/Jun/2025:02:01:06 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:01:06'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:01:21'
}
{
  message: '::1 - - [26/Jun/2025:02:02:03 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:03'
}
{
  message: '::1 - - [26/Jun/2025:02:02:03 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '8ee39e38...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 19:02:24'
}
{
  message: '::1 - - [26/Jun/2025:02:02:24 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:24'
}
{
  message: '::1 - - [26/Jun/2025:02:02:31 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:31'
}
{
  message: '::1 - - [26/Jun/2025:02:02:31 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:31'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:31'
}
{
  message: 'Using demo access token for development',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  facebookUserId: '****************',
  permissions: 0,
  level: 'info',
  message: 'Facebook OAuth completed successfully',
  timestamp: '2025-06-25 19:02:31'
}
{
  message: '::1 - - [26/Jun/2025:02:02:31 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 200 630 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:31'
}
{
  message: '::1 - - [26/Jun/2025:02:02:36 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:36'
}
{
  message: '::1 - - [26/Jun/2025:02:02:36 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '6832eb13...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 19:02:44'
}
{
  message: '::1 - - [26/Jun/2025:02:02:44 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:44'
}
{
  message: '::1 - - [26/Jun/2025:02:02:49 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:49'
}
{
  message: '::1 - - [26/Jun/2025:02:02:49 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:49'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:49'
}
{
  message: 'Using demo access token for development',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  facebookUserId: '****************',
  permissions: 0,
  level: 'info',
  message: 'Facebook OAuth completed successfully',
  timestamp: '2025-06-25 19:02:50'
}
{
  message: '::1 - - [26/Jun/2025:02:02:50 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 200 630 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:02:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '5186383f...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 19:03:57'
}
{
  message: '::1 - - [26/Jun/2025:02:03:57 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001/ HTTP/1.1" 200 470 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:03:57'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:12:29'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:25'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:25'
}
{
  message: 'Redis disabled in configuration',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:25'
}
{
  message: 'Notification service initialized',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:25'
}
{
  message: 'Server initialized successfully',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:26'
}
{
  message: '🚀 Pressure Max API server running on port 3000',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:26'
}
{
  message: '📚 API Documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:26'
}
{
  message: '🏥 Health check available at http://localhost:3000/health',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:26'
}
{
  message: '::1 - - [26/Jun/2025:02:18:40 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:40'
}
{
  message: '::1 - - [26/Jun/2025:02:18:40 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:40'
}
{
  message: 'Redis not available. Server-side state validation will be skipped.',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: 'dd27cc0e...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 19:18:41'
}
{
  message: '::1 - - [26/Jun/2025:02:18:41 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:41'
}
{
  message: '::1 - - [26/Jun/2025:02:18:47 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:47'
}
{
  message: '::1 - - [26/Jun/2025:02:18:47 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:47'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:47'
}
{
  message: 'Using demo access token for development',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:47'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  facebookUserId: '****************',
  permissions: 0,
  level: 'info',
  message: 'Facebook OAuth completed successfully',
  timestamp: '2025-06-25 19:18:48'
}
{
  message: '::1 - - [26/Jun/2025:02:18:48 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 200 630 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:18:48'
}
{
  message: 'Redis not available. Server-side state validation will be skipped.',
  level: 'warn',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:19:08'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  redirectUri: 'http://localhost:3001/',
  state: '6ccfdd4f...',
  level: 'info',
  message: 'Public OAuth URL generated',
  timestamp: '2025-06-25 19:19:08'
}
{
  message: '::1 - - [26/Jun/2025:02:19:08 +0000] "GET /api/v1/facebook-public/oauth-url?redirectUri=http:%2F%2Flocalhost:3001%2F HTTP/1.1" 200 470 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:19:08'
}
{
  message: '::1 - - [26/Jun/2025:02:19:16 +0000] "GET /health HTTP/1.1" 200 97 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:19:16'
}
{
  message: '::1 - - [26/Jun/2025:02:19:16 +0000] "GET /health HTTP/1.1" 200 96 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:19:16'
}
{
  message: 'Redis not available, skipping server-side state verification',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:19:16'
}
{
  message: 'Using demo access token for development',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:19:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  facebookUserId: '****************',
  permissions: 0,
  level: 'info',
  message: 'Facebook OAuth completed successfully',
  timestamp: '2025-06-25 19:19:17'
}
{
  message: '::1 - - [26/Jun/2025:02:19:17 +0000] "POST /api/v1/facebook-public/oauth-callback HTTP/1.1" 200 630 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:19:17'
}
{
  message: '::1 - - [26/Jun/2025:02:21:30 +0000] "GET /main.98798842def46b832a89.hot-update.json HTTP/1.1" 404 120 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:21:30'
}
{
  message: '::1 - - [26/Jun/2025:02:21:31 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:21:31'
}
{
  message: '::1 - - [26/Jun/2025:02:21:31 +0000] "GET /health HTTP/1.1" 200 98 "http://localhost:3001/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 19:21:31'
}
