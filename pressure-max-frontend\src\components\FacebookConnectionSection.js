import React, { useState, useEffect } from 'react';
import { useFacebook } from '../contexts/FacebookContext';
import { facebookPublicAPI } from '../services/api';
import toast from 'react-hot-toast';
import { Facebook, Users, CreditCard, AlertCircle, CheckCircle, LogOut, TestTube } from 'lucide-react';

const FacebookConnectionSection = () => {
  const { 
    facebookUser, 
    accessToken, 
    loading, 
    isConnected, 
    initiateFacebookLogin, 
    logout, 
    testConnection 
  } = useFacebook();
  
  const [adAccounts, setAdAccounts] = useState([]);
  const [loadingAccounts, setLoadingAccounts] = useState(false);

  useEffect(() => {
    if (isConnected && accessToken) {
      loadAdAccounts();
    }
  }, [isConnected, accessToken]);

  const loadAdAccounts = async () => {
    try {
      setLoadingAccounts(true);
      const response = await facebookPublicAPI.getAdAccounts(accessToken);
      setAdAccounts(response.data?.data || []);
    } catch (error) {
      console.error('Failed to load ad accounts:', error);
      toast.error('Failed to load ad accounts');
      setAdAccounts([]);
    } finally {
      setLoadingAccounts(false);
    }
  };

  const handleTestConnection = async () => {
    await testConnection();
  };

  if (loading) {
    return (
      <div className="facebook-section">
        <h2>Facebook Integration</h2>
        <div className="loading">
          <p>Loading Facebook connection...</p>
        </div>
      </div>
    );
  }

  if (!isConnected) {
    return (
      <div className="facebook-section">
        <h2>Facebook Integration</h2>
        <div className="connection-status disconnected">
          <Facebook size={24} />
          <div className="status-info">
            <h3>Not Connected</h3>
            <p>Connect your Facebook account to manage campaigns and access ad accounts.</p>
          </div>
        </div>
        
        <div className="connection-actions">
          <button 
            onClick={initiateFacebookLogin}
            className="connect-button"
            disabled={loading}
          >
            <Facebook size={16} />
            Connect Facebook Account
          </button>
        </div>

        <div className="connection-info">
          <h4>What you'll get:</h4>
          <ul>
            <li>Access to your Facebook ad accounts</li>
            <li>Campaign creation and management</li>
            <li>Real-time campaign data</li>
            <li>Performance insights</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="facebook-section">
      <h2>Facebook Integration</h2>
      
      <div className="connection-status connected">
        <CheckCircle size={24} color="#10b981" />
        <div className="status-info">
          <h3>Connected</h3>
          <p>Successfully connected to Facebook</p>
        </div>
      </div>

      {facebookUser && (
        <div className="user-info">
          <h4>Connected Account</h4>
          <div className="user-details">
            <p><strong>Name:</strong> {facebookUser.name}</p>
            <p><strong>ID:</strong> {facebookUser.id}</p>
          </div>
        </div>
      )}

      <div className="ad-accounts">
        <h4>Ad Accounts</h4>
        {loadingAccounts ? (
          <p>Loading ad accounts...</p>
        ) : adAccounts.length > 0 ? (
          <div className="accounts-list">
            {adAccounts.map((account) => (
              <div key={account.id} className="account-item">
                <CreditCard size={16} />
                <div className="account-info">
                  <span className="account-name">{account.name}</span>
                  <span className="account-id">{account.id}</span>
                  <span className="account-currency">{account.currency}</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-accounts">
            <AlertCircle size={16} />
            <p>No ad accounts found</p>
          </div>
        )}
      </div>

      <div className="connection-actions">
        <button 
          onClick={handleTestConnection}
          className="test-button"
        >
          <TestTube size={16} />
          Test Connection
        </button>
        
        <button 
          onClick={logout}
          className="disconnect-button"
        >
          <LogOut size={16} />
          Disconnect
        </button>
      </div>
    </div>
  );
};

export default FacebookConnectionSection;
