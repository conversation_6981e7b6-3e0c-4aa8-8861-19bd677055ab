{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\AuthSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthSection = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    console.log('AuthSection useEffect: Checking for OAuth callback', {\n      code: code ? 'present' : 'missing',\n      state: state ? state.substring(0, 8) + '...' : 'missing',\n      isAuthenticated,\n      currentUrl: window.location.href,\n      localStorageKeys: Object.keys(localStorage)\n    });\n    if (code && state && !isAuthenticated) {\n      console.log('AuthSection: Processing OAuth callback');\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n  const onLogin = async data => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      console.log('AuthSection: Requesting OAuth URL for redirectUri:', redirectUri);\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('AuthSection: OAuth URL response:', response.data);\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('AuthSection: Verified stored state:', storedState);\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n        console.log('AuthSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      console.error('AuthSection: Facebook login error:', error);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state,\n        localStorageKeys: Object.keys(localStorage),\n        currentUrl: window.location.href\n      });\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', {\n          receivedState: state,\n          storedState\n        });\n\n        // In development mode with demo token, skip client-side state verification\n        // since the backend handles this and uses a demo token anyway\n        if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {\n          console.warn('Development mode: Skipping client-side state verification');\n          toast.warning('Development mode: Proceeding without state verification');\n        } else {\n          setLoading(false);\n          toast.error('Authentication failed: Invalid state parameter. Please try again.');\n          // Clear the URL parameters to clean up the UI\n          window.history.replaceState({}, document.title, window.location.pathname);\n          return; // Don't proceed with the API call\n        }\n      }\n\n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n\n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-status authenticated\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Role: \", user.role]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), user.facebookConnected && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#1877f2'\n              },\n              children: \"\\u2705 Facebook Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"token-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"JWT Token:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowToken(!showToken),\n              className: \"toggle-token\",\n              children: showToken ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), showToken && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-display\",\n            children: /*#__PURE__*/_jsxDEV(\"code\", {\n              children: getToken()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), \"Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Authentication\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'facebook' ? 'active' : '',\n        onClick: () => setActiveTab('facebook'),\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), \"Facebook Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'login' ? 'active' : '',\n        onClick: () => setActiveTab('login'),\n        children: \"Email Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'register' ? 'active' : '',\n        onClick: () => setActiveTab('register'),\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-login-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"facebook-login-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Facebook, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), \"Login with Facebook\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect your Facebook Business Manager account to access:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Ad account management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Campaign creation and monitoring\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Lead generation forms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Marketing API permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 No separate email/password required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleFacebookLogin,\n        disabled: loading,\n        className: \"facebook-login-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"facebook-note\",\n        children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"***************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 55\n        }, this), \"This will automatically grant marketing API permissions.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: loginForm.handleSubmit(onLogin),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...loginForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...loginForm.register('password', {\n            required: 'Password is required'\n          }),\n          placeholder: \"Enter your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: registerForm.handleSubmit(onRegister),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"First Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('firstName', {\n            required: 'First name is required'\n          }),\n          placeholder: \"Enter your first name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.firstName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Last Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('lastName', {\n            required: 'Last name is required'\n          }),\n          placeholder: \"Enter your last name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.lastName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...registerForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...registerForm.register('password', {\n            required: 'Password is required',\n            minLength: {\n              value: 8,\n              message: 'Password must be at least 8 characters'\n            }\n          }),\n          placeholder: \"Enter your password (min 8 characters)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Phone (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"tel\",\n          ...registerForm.register('phone'),\n          placeholder: \"Enter your phone number\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Registering...' : 'Register'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthSection, \"J+0be1bUuf2hGRBagTwIZhTtrCY=\", false, function () {\n  return [useAuth, useForm, useForm];\n});\n_c = AuthSection;\nexport default AuthSection;\nvar _c;\n$RefreshReg$(_c, \"AuthSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useForm", "facebookAPI", "User", "LogOut", "Eye", "Eye<PERSON>ff", "Facebook", "toast", "jsxDEV", "_jsxDEV", "AuthSection", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "activeTab", "setActiveTab", "showToken", "setShowToken", "loading", "setLoading", "loginForm", "registerForm", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "console", "log", "substring", "currentUrl", "href", "localStorageKeys", "Object", "keys", "localStorage", "handleFacebookCallback", "onLogin", "data", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "response", "getOAuthUrl", "oauthUrl", "removeItem", "setItem", "storedState", "getItem", "Error", "error", "message", "receivedState", "match", "process", "env", "NODE_ENV", "hostname", "warn", "warning", "history", "replaceState", "document", "title", "pathname", "handleOAuthCallback", "tokens", "accessToken", "refreshToken", "JSON", "stringify", "reload", "success", "url", "URL", "searchParams", "delete", "toString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "lastName", "email", "role", "facebookConnected", "style", "color", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "password", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/AuthSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst AuthSection = () => {\n  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n\n    console.log('AuthSection useEffect: Checking for OAuth callback', {\n      code: code ? 'present' : 'missing',\n      state: state ? state.substring(0, 8) + '...' : 'missing',\n      isAuthenticated,\n      currentUrl: window.location.href,\n      localStorageKeys: Object.keys(localStorage)\n    });\n\n    if (code && state && !isAuthenticated) {\n      console.log('AuthSection: Processing OAuth callback');\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      console.log('AuthSection: Requesting OAuth URL for redirectUri:', redirectUri);\n\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('AuthSection: OAuth URL response:', response.data);\n\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('AuthSection: Verified stored state:', storedState);\n\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n\n        console.log('AuthSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      console.error('AuthSection: Facebook login error:', error);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state,\n        localStorageKeys: Object.keys(localStorage),\n        currentUrl: window.location.href\n      });\n\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', { receivedState: state, storedState });\n\n        // In development mode with demo token, skip client-side state verification\n        // since the backend handles this and uses a demo token anyway\n        if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {\n          console.warn('Development mode: Skipping client-side state verification');\n          toast.warning('Development mode: Proceeding without state verification');\n        } else {\n          setLoading(false);\n          toast.error('Authentication failed: Invalid state parameter. Please try again.');\n          // Clear the URL parameters to clean up the UI\n          window.history.replaceState({}, document.title, window.location.pathname);\n          return; // Don't proceed with the API call\n        }\n      }\n      \n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      \n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      \n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        \n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n        \n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      \n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n\n  if (isAuthenticated) {\n    return (\n      <div className=\"auth-section\">\n        <h2>Authentication Status</h2>\n        <div className=\"auth-status authenticated\">\n          <div className=\"user-info\">\n            <User size={20} />\n            <div>\n              <p><strong>{user.firstName} {user.lastName}</strong></p>\n              <p>{user.email}</p>\n              <p>Role: {user.role}</p>\n              {user.facebookConnected && (\n                <p style={{color: '#1877f2'}}>✅ Facebook Connected</p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"token-section\">\n            <div className=\"token-header\">\n              <span>JWT Token:</span>\n              <button \n                onClick={() => setShowToken(!showToken)}\n                className=\"toggle-token\"\n              >\n                {showToken ? <EyeOff size={16} /> : <Eye size={16} />}\n              </button>\n            </div>\n            {showToken && (\n              <div className=\"token-display\">\n                <code>{getToken()}</code>\n              </div>\n            )}\n          </div>\n\n          <button onClick={logout} className=\"logout-btn\">\n            <LogOut size={16} />\n            Logout\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-section\">\n      <h2>Authentication</h2>\n      \n      <div className=\"auth-tabs\">\n        <button \n          className={activeTab === 'facebook' ? 'active' : ''}\n          onClick={() => setActiveTab('facebook')}\n        >\n          <Facebook size={16} />\n          Facebook Login\n        </button>\n        <button \n          className={activeTab === 'login' ? 'active' : ''}\n          onClick={() => setActiveTab('login')}\n        >\n          Email Login\n        </button>\n        <button \n          className={activeTab === 'register' ? 'active' : ''}\n          onClick={() => setActiveTab('register')}\n        >\n          Register\n        </button>\n      </div>\n\n      {activeTab === 'facebook' ? (\n        <div className=\"facebook-login-section\">\n          <div className=\"facebook-login-info\">\n            <h3>\n              <Facebook size={20} />\n              Login with Facebook\n            </h3>\n            <p>Connect your Facebook Business Manager account to access:</p>\n            <ul>\n              <li>✅ Ad account management</li>\n              <li>✅ Campaign creation and monitoring</li>\n              <li>✅ Lead generation forms</li>\n              <li>✅ Marketing API permissions</li>\n              <li>✅ No separate email/password required</li>\n            </ul>\n          </div>\n          \n          <button \n            onClick={handleFacebookLogin}\n            disabled={loading}\n            className=\"facebook-login-btn\"\n          >\n            <Facebook size={20} />\n            {loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager'}\n          </button>\n          \n          <p className=\"facebook-note\">\n            Using App ID: <code>***************</code><br/>\n            This will automatically grant marketing API permissions.\n          </p>\n        </div>\n      ) : activeTab === 'login' ? (\n        <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...loginForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {loginForm.formState.errors.email && (\n              <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...loginForm.register('password', { required: 'Password is required' })}\n              placeholder=\"Enter your password\"\n            />\n            {loginForm.formState.errors.password && (\n              <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n      ) : (\n        <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>First Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('firstName', { required: 'First name is required' })}\n              placeholder=\"Enter your first name\"\n            />\n            {registerForm.formState.errors.firstName && (\n              <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Last Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('lastName', { required: 'Last name is required' })}\n              placeholder=\"Enter your last name\"\n            />\n            {registerForm.formState.errors.lastName && (\n              <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...registerForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {registerForm.formState.errors.email && (\n              <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...registerForm.register('password', { \n                required: 'Password is required',\n                minLength: { value: 8, message: 'Password must be at least 8 characters' }\n              })}\n              placeholder=\"Enter your password (min 8 characters)\"\n            />\n            {registerForm.formState.errors.password && (\n              <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Phone (optional):</label>\n            <input\n              type=\"tel\"\n              {...registerForm.register('phone')}\n              placeholder=\"Enter your phone number\"\n            />\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Registering...' : 'Register'}\n          </button>\n        </form>\n      )}\n    </div>\n  );\n};\n\nexport default AuthSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AAClE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC9E,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM2B,SAAS,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,YAAY,GAAGzB,OAAO,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,MAAM4B,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpCE,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;MAChEJ,IAAI,EAAEA,IAAI,GAAG,SAAS,GAAG,SAAS;MAClCE,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,SAAS;MACxDvB,eAAe;MACfwB,UAAU,EAAET,MAAM,CAACC,QAAQ,CAACS,IAAI;MAChCC,gBAAgB,EAAEC,MAAM,CAACC,IAAI,CAACC,YAAY;IAC5C,CAAC,CAAC;IAEF,IAAIX,IAAI,IAAIE,KAAK,IAAI,CAACpB,eAAe,EAAE;MACrCqB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDQ,sBAAsB,CAACZ,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACpB,eAAe,CAAC,CAAC;EAErB,MAAM+B,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9BtB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMT,KAAK,CAAC+B,IAAI,CAAC;IACjBtB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMuB,UAAU,GAAG,MAAOD,IAAI,IAAK;IACjCtB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMR,QAAQ,CAAC8B,IAAI,CAAC;IACpBtB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMwB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,WAAW,GAAG,GAAGpB,MAAM,CAACC,QAAQ,CAACoB,MAAM,GAAG;MAChDf,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEa,WAAW,CAAC;MAE9E,MAAME,QAAQ,GAAG,MAAMjD,WAAW,CAACkD,WAAW,CAACH,WAAW,CAAC;MAC3Dd,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEe,QAAQ,CAACL,IAAI,CAAC;MAE9D,IAAIK,QAAQ,CAACL,IAAI,CAACO,QAAQ,IAAIF,QAAQ,CAACL,IAAI,CAACZ,KAAK,EAAE;QACjD;QACAC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEe,QAAQ,CAACL,IAAI,CAACZ,KAAK,CAAC;QACrE;QACAS,YAAY,CAACW,UAAU,CAAC,sBAAsB,CAAC;QAC/CX,YAAY,CAACY,OAAO,CAAC,sBAAsB,EAAEJ,QAAQ,CAACL,IAAI,CAACZ,KAAK,CAAC;;QAEjE;QACA,MAAMsB,WAAW,GAAGb,YAAY,CAACc,OAAO,CAAC,sBAAsB,CAAC;QAChEtB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEoB,WAAW,CAAC;QAE/D,IAAIA,WAAW,KAAKL,QAAQ,CAACL,IAAI,CAACZ,KAAK,EAAE;UACvC,MAAM,IAAIwB,KAAK,CAAC,uCAAuC,CAAC;QAC1D;QAEAvB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7DP,MAAM,CAACC,QAAQ,CAACS,IAAI,GAAGY,QAAQ,CAACL,IAAI,CAACO,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAAC,4CAA4C,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdnC,UAAU,CAAC,KAAK,CAAC;MACjBW,OAAO,CAACwB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DnD,KAAK,CAACmD,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAACC,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMhB,sBAAsB,GAAG,MAAAA,CAAOZ,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMgC,WAAW,GAAGb,YAAY,CAACc,OAAO,CAAC,sBAAsB,CAAC;MAChEtB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCyB,aAAa,EAAE3B,KAAK;QACpBsB,WAAW;QACXM,KAAK,EAAEN,WAAW,KAAKtB,KAAK;QAC5BM,gBAAgB,EAAEC,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC;QAC3CL,UAAU,EAAET,MAAM,CAACC,QAAQ,CAACS;MAC9B,CAAC,CAAC;MAEF,IAAI,CAACiB,WAAW,IAAIA,WAAW,KAAKtB,KAAK,EAAE;QACzCC,OAAO,CAACwB,KAAK,CAAC,2BAA2B,EAAE;UAAEE,aAAa,EAAE3B,KAAK;UAAEsB;QAAY,CAAC,CAAC;;QAEjF;QACA;QACA,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIpC,MAAM,CAACC,QAAQ,CAACoC,QAAQ,KAAK,WAAW,EAAE;UACtF/B,OAAO,CAACgC,IAAI,CAAC,2DAA2D,CAAC;UACzE3D,KAAK,CAAC4D,OAAO,CAAC,yDAAyD,CAAC;QAC1E,CAAC,MAAM;UACL5C,UAAU,CAAC,KAAK,CAAC;UACjBhB,KAAK,CAACmD,KAAK,CAAC,mEAAmE,CAAC;UAChF;UACA9B,MAAM,CAACwC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAE3C,MAAM,CAACC,QAAQ,CAAC2C,QAAQ,CAAC;UACzE,OAAO,CAAC;QACV;MACF;;MAEA;MACA9B,YAAY,CAACW,UAAU,CAAC,sBAAsB,CAAC;MAE/C,MAAML,WAAW,GAAG,GAAGpB,MAAM,CAACC,QAAQ,CAACoB,MAAM,GAAG;MAChD,MAAMC,QAAQ,GAAG,MAAMjD,WAAW,CAACwE,mBAAmB,CAAC1C,IAAI,EAAEE,KAAK,EAAEe,WAAW,CAAC;MAEhF,IAAIE,QAAQ,CAACL,IAAI,CAACjC,IAAI,IAAIsC,QAAQ,CAACL,IAAI,CAAC6B,MAAM,EAAE;QAC9C;QACAhC,YAAY,CAACY,OAAO,CAAC,aAAa,EAAEJ,QAAQ,CAACL,IAAI,CAAC6B,MAAM,CAACC,WAAW,CAAC;QACrEjC,YAAY,CAACY,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACL,IAAI,CAAC6B,MAAM,CAACE,YAAY,CAAC;QACvElC,YAAY,CAACY,OAAO,CAAC,MAAM,EAAEuB,IAAI,CAACC,SAAS,CAAC5B,QAAQ,CAACL,IAAI,CAACjC,IAAI,CAAC,CAAC;;QAEhE;QACAgB,MAAM,CAACC,QAAQ,CAACkD,MAAM,CAAC,CAAC,CAAC,CAAC;;QAE1BxE,KAAK,CAACyE,OAAO,CAAC,uCAAuC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdnC,UAAU,CAAC,KAAK,CAAC;MACjBhB,KAAK,CAACmD,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAACC,OAAO,CAAC;;MAEtD;MACA,MAAMsB,GAAG,GAAG,IAAIC,GAAG,CAACtD,MAAM,CAACC,QAAQ,CAAC;MACpCoD,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;MAC/BH,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;MAChCxD,MAAM,CAACwC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEU,GAAG,CAACI,QAAQ,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;EAED,IAAIxE,eAAe,EAAE;IACnB,oBACEJ,OAAA;MAAK6E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B9E,OAAA;QAAA8E,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BlF,OAAA;QAAK6E,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC9E,OAAA;UAAK6E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9E,OAAA,CAACP,IAAI;YAAC0F,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClBlF,OAAA;YAAA8E,QAAA,gBACE9E,OAAA;cAAA8E,QAAA,eAAG9E,OAAA;gBAAA8E,QAAA,GAAS3E,IAAI,CAACiF,SAAS,EAAC,GAAC,EAACjF,IAAI,CAACkF,QAAQ;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDlF,OAAA;cAAA8E,QAAA,EAAI3E,IAAI,CAACmF;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBlF,OAAA;cAAA8E,QAAA,GAAG,QAAM,EAAC3E,IAAI,CAACoF,IAAI;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvB/E,IAAI,CAACqF,iBAAiB,iBACrBxF,OAAA;cAAGyF,KAAK,EAAE;gBAACC,KAAK,EAAE;cAAS,CAAE;cAAAZ,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK6E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9E,OAAA;YAAK6E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B9E,OAAA;cAAA8E,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBlF,OAAA;cACE2F,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,CAACD,SAAS,CAAE;cACxCkE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAEvBnE,SAAS,gBAAGX,OAAA,CAACJ,MAAM;gBAACuF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGlF,OAAA,CAACL,GAAG;gBAACwF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLvE,SAAS,iBACRX,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B9E,OAAA;cAAA8E,QAAA,EAAOtE,QAAQ,CAAC;YAAC;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlF,OAAA;UAAQ2F,OAAO,EAAEpF,MAAO;UAACsE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC7C9E,OAAA,CAACN,MAAM;YAACyF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElF,OAAA;IAAK6E,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B9E,OAAA;MAAA8E,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvBlF,OAAA;MAAK6E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB9E,OAAA;QACE6E,SAAS,EAAEpE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDkF,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,UAAU,CAAE;QAAAoE,QAAA,gBAExC9E,OAAA,CAACH,QAAQ;UAACsF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlF,OAAA;QACE6E,SAAS,EAAEpE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;QACjDkF,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,OAAO,CAAE;QAAAoE,QAAA,EACtC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlF,OAAA;QACE6E,SAAS,EAAEpE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDkF,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,UAAU,CAAE;QAAAoE,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELzE,SAAS,KAAK,UAAU,gBACvBT,OAAA;MAAK6E,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrC9E,OAAA;QAAK6E,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC9E,OAAA;UAAA8E,QAAA,gBACE9E,OAAA,CAACH,QAAQ;YAACsF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlF,OAAA;UAAA8E,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChElF,OAAA;UAAA8E,QAAA,gBACE9E,OAAA;YAAA8E,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChClF,OAAA;YAAA8E,QAAA,EAAI;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ClF,OAAA;YAAA8E,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChClF,OAAA;YAAA8E,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpClF,OAAA;YAAA8E,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENlF,OAAA;QACE2F,OAAO,EAAErD,mBAAoB;QAC7BsD,QAAQ,EAAE/E,OAAQ;QAClBgE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAE9B9E,OAAA,CAACH,QAAQ;UAACsF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBrE,OAAO,GAAG,2BAA2B,GAAG,sCAAsC;MAAA;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAETlF,OAAA;QAAG6E,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,gBACb,eAAA9E,OAAA;UAAA8E,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAAAlF,OAAA;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,4DAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,GACJzE,SAAS,KAAK,OAAO,gBACvBT,OAAA;MAAM6F,QAAQ,EAAE9E,SAAS,CAAC+E,YAAY,CAAC3D,OAAO,CAAE;MAAC0C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpE9E,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9E,OAAA;UAAA8E,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBlF,OAAA;UACE+F,IAAI,EAAC,OAAO;UAAA,GACRhF,SAAS,CAACT,QAAQ,CAAC,OAAO,EAAE;YAAE0F,QAAQ,EAAE;UAAoB,CAAC,CAAC;UAClEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDnE,SAAS,CAACmF,SAAS,CAACC,MAAM,CAACb,KAAK,iBAC/BtF,OAAA;UAAM6E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/D,SAAS,CAACmF,SAAS,CAACC,MAAM,CAACb,KAAK,CAACpC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlF,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9E,OAAA;UAAA8E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBlF,OAAA;UACE+F,IAAI,EAAC,UAAU;UAAA,GACXhF,SAAS,CAACT,QAAQ,CAAC,UAAU,EAAE;YAAE0F,QAAQ,EAAE;UAAuB,CAAC,CAAC;UACxEC,WAAW,EAAC;QAAqB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDnE,SAAS,CAACmF,SAAS,CAACC,MAAM,CAACC,QAAQ,iBAClCpG,OAAA;UAAM6E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/D,SAAS,CAACmF,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAClD;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlF,OAAA;QAAQ+F,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE/E,OAAQ;QAACgE,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DjE,OAAO,GAAG,eAAe,GAAG;MAAO;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEPlF,OAAA;MAAM6F,QAAQ,EAAE7E,YAAY,CAAC8E,YAAY,CAACzD,UAAU,CAAE;MAACwC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC1E9E,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9E,OAAA;UAAA8E,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BlF,OAAA;UACE+F,IAAI,EAAC,MAAM;UAAA,GACP/E,YAAY,CAACV,QAAQ,CAAC,WAAW,EAAE;YAAE0F,QAAQ,EAAE;UAAyB,CAAC,CAAC;UAC9EC,WAAW,EAAC;QAAuB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDlE,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACf,SAAS,iBACtCpF,OAAA;UAAM6E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9D,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACf,SAAS,CAAClC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlF,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9E,OAAA;UAAA8E,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzBlF,OAAA;UACE+F,IAAI,EAAC,MAAM;UAAA,GACP/E,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YAAE0F,QAAQ,EAAE;UAAwB,CAAC,CAAC;UAC5EC,WAAW,EAAC;QAAsB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACDlE,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACd,QAAQ,iBACrCrF,OAAA;UAAM6E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9D,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACd,QAAQ,CAACnC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlF,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9E,OAAA;UAAA8E,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBlF,OAAA;UACE+F,IAAI,EAAC,OAAO;UAAA,GACR/E,YAAY,CAACV,QAAQ,CAAC,OAAO,EAAE;YAAE0F,QAAQ,EAAE;UAAoB,CAAC,CAAC;UACrEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDlE,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACb,KAAK,iBAClCtF,OAAA;UAAM6E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9D,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACb,KAAK,CAACpC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlF,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9E,OAAA;UAAA8E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBlF,OAAA;UACE+F,IAAI,EAAC,UAAU;UAAA,GACX/E,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YACpC0F,QAAQ,EAAE,sBAAsB;YAChCK,SAAS,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAEpD,OAAO,EAAE;YAAyC;UAC3E,CAAC,CAAC;UACF+C,WAAW,EAAC;QAAwC;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EACDlE,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACC,QAAQ,iBACrCpG,OAAA;UAAM6E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9D,YAAY,CAACkF,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAClD;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlF,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9E,OAAA;UAAA8E,QAAA,EAAO;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChClF,OAAA;UACE+F,IAAI,EAAC,KAAK;UAAA,GACN/E,YAAY,CAACV,QAAQ,CAAC,OAAO,CAAC;UAClC2F,WAAW,EAAC;QAAyB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlF,OAAA;QAAQ+F,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE/E,OAAQ;QAACgE,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DjE,OAAO,GAAG,gBAAgB,GAAG;MAAU;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChF,EAAA,CAhVID,WAAW;EAAA,QACsDX,OAAO,EAK1DC,OAAO,EACJA,OAAO;AAAA;AAAAgH,EAAA,GAPxBtG,WAAW;AAkVjB,eAAeA,WAAW;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}