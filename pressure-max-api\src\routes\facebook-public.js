const express = require('express');
const router = express.Router();

// Test route
router.get('/test', (req, res) => {
  res.json({ message: 'Facebook public routes working' });
});

module.exports = router;
 *               properties:
 *                 success:
 *                   type: boolean
 *                 oauthUrl:
 *                   type: string
 *                 state:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.get('/oauth-url', [
  query('redirectUri').notEmpty().withMessage('Redirect URI is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { redirectUri } = req.query;
  
  // Generate state parameter for security
  const state = crypto.randomBytes(32).toString('hex');
  
  // For public OAuth, we'll store the state in a temporary way
  // Store state in Redis for verification (if available)
  try {
    if (req.app.locals.redis && req.app.locals.redis.set) {
      // CRITICAL FIX: Serialize the object to JSON string before storing
      const stateData = {
        redirectUri,
        timestamp: Date.now()
      };
      await req.app.locals.redis.set(`public_oauth_state:${state}`, JSON.stringify(stateData), 'EX', 600);
      logger.info('Successfully stored OAuth state in Redis', { state: state.substring(0, 8) + '...' });
    } else {
      logger.warn('Redis not available. Server-side state validation will be skipped.');
    }
  } catch (error) {
    logger.error('CRITICAL: Could not store OAuth state in Redis:', error);
    // Continue without Redis state storage for development
  }

  const oauthUrl = facebookService.generateOAuthUrl(redirectUri, state);

  logger.info('Public OAuth URL generated', {
    redirectUri,
    state: state.substring(0, 8) + '...' // Log partial state for debugging
  });

  res.json({
    success: true,
    oauthUrl,
    state,
    message: 'OAuth URL generated successfully'
  });
}));

/**
 * @swagger
 * /facebook-public/oauth-callback:
 *   post:
 *     summary: Handle Facebook OAuth callback (Public)
 *     tags: [Facebook Public]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - state
 *             properties:
 *               code:
 *                 type: string
 *               state:
 *                 type: string
 *               redirectUri:
 *                 type: string
 *     responses:
 *       200:
 *         description: OAuth callback processed successfully
 */
router.post('/oauth-callback', [
  body('code').notEmpty().withMessage('Authorization code is required'),
  body('state').notEmpty().withMessage('State parameter is required'),
  body('redirectUri').optional().isURL({ require_tld: false, require_protocol: true }).withMessage('Valid redirect URI is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { code, state, redirectUri } = req.body;

  // Verify state parameter if Redis is available
  let stateVerified = false;
  try {
    if (req.app.locals.redis && req.app.locals.redis.get) {
      const storedDataString = await req.app.locals.redis.get(`public_oauth_state:${state}`);
      if (storedDataString) {
        // CRITICAL FIX: Parse the JSON string back into an object
        const storedData = JSON.parse(storedDataString);
        logger.info('OAuth state verified successfully', {
          state: state.substring(0, 8) + '...',
          storedRedirectUri: storedData.redirectUri
        });

        // Clean up state
        await req.app.locals.redis.del(`public_oauth_state:${state}`);
        stateVerified = true;
      } else {
        logger.warn('OAuth state not found in Redis', { state: state.substring(0, 8) + '...' });
      }
    } else {
      logger.info('Redis not available, skipping server-side state verification');
      // When Redis is not available, we rely on client-side state verification
      // The frontend should verify the state parameter matches what it stored
      stateVerified = true;
    }
  } catch (error) {
    logger.warn('Could not verify OAuth state:', error.message);
    // Continue without server-side state verification
    stateVerified = true;
  }

  if (!stateVerified) {
    logger.error('OAuth state verification failed', { state: state.substring(0, 8) + '...' });
    throw new ValidationError('Invalid state parameter');
  }

  try {
    // For development/testing: Check if we have a demo access token
    let tokenData;
    let longLivedToken;
    const demoAccessToken = process.env.FACEBOOK_DEMO_ACCESS_TOKEN;

    if (demoAccessToken && process.env.NODE_ENV === 'development') {
      logger.info('Using demo access token for development');
      tokenData = {
        access_token: demoAccessToken,
        token_type: 'bearer'
      };
      longLivedToken = { access_token: demoAccessToken };
    } else {
      // Exchange code for access token
      tokenData = await facebookService.exchangeCodeForToken(code, redirectUri);

      // Get long-lived token
      longLivedToken = await facebookService.getLongLivedToken(tokenData.access_token);
    }
    
    // Get user profile
    const profile = await facebookService.getUserProfile(longLivedToken.access_token);
    
    // Get permissions
    const permissions = tokenData.scope ? tokenData.scope.split(',') : [];

    logger.info('Facebook OAuth completed successfully', {
      facebookUserId: profile.id,
      permissions: permissions.length
    });

    res.json({
      success: true,
      message: 'Facebook OAuth completed successfully',
      profile,
      permissions,
      accessToken: longLivedToken.access_token,
      expiresIn: longLivedToken.expires_in
    });
  } catch (error) {
    logger.error('Facebook OAuth callback error:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook-public/test:
 *   get:
 *     summary: Test endpoint for public Facebook routes
 *     tags: [Facebook Public]
 *     responses:
 *       200:
 *         description: Test successful
 */
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Public Facebook routes are working',
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /facebook-public/test-token:
 *   post:
 *     summary: Test Facebook API with access token
 *     tags: [Facebook Public]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - accessToken
 *             properties:
 *               accessToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token test successful
 */
router.post('/test-token', [
  body('accessToken').notEmpty().withMessage('Access token is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { accessToken } = req.body;

  try {
    // Test user profile
    const profile = await facebookService.getUserProfile(accessToken);

    // Test ad accounts
    const adAccounts = await facebookService.getAdAccounts(accessToken);

    logger.info('Facebook token test successful', {
      userId: profile.id,
      userName: profile.name,
      adAccountsCount: adAccounts.length
    });

    res.json({
      success: true,
      message: 'Facebook API test successful',
      data: {
        profile,
        adAccounts,
        adAccountsCount: adAccounts.length
      }
    });
  } catch (error) {
    logger.error('Facebook token test failed:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook-public/campaigns:
 *   post:
 *     summary: Create a new Facebook campaign (Public)
 *     tags: [Facebook Public]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - accessToken
 *               - adAccountId
 *               - name
 *               - objective
 *             properties:
 *               accessToken:
 *                 type: string
 *               adAccountId:
 *                 type: string
 *               name:
 *                 type: string
 *               objective:
 *                 type: string
 *               status:
 *                 type: string
 *               specialAdCategories:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: Campaign created successfully
 */
router.post('/campaigns', [
  body('accessToken').notEmpty().withMessage('Access token is required'),
  body('adAccountId').notEmpty().withMessage('Ad account ID is required'),
  body('name').trim().isLength({ min: 1 }).withMessage('Campaign name is required'),
  body('objective').notEmpty().withMessage('Campaign objective is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { accessToken, adAccountId, ...campaignData } = req.body;

  try {
    // Check rate limit (using a generic identifier since no user authentication)
    await facebookService.checkRateLimit('public_user');

    // Create campaign via Facebook API
    const campaign = await facebookService.createCampaign(
      accessToken,
      adAccountId,
      campaignData
    );

    logger.info('Public Facebook campaign created', {
      campaignId: campaign.id,
      adAccountId,
      campaignName: campaignData.name
    });

    res.status(201).json({
      success: true,
      message: 'Campaign created successfully',
      campaign
    });
  } catch (error) {
    logger.error('Error creating public campaign:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook-public/ad-accounts:
 *   post:
 *     summary: Get Facebook ad accounts (Public)
 *     tags: [Facebook Public]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - accessToken
 *             properties:
 *               accessToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Ad accounts retrieved successfully
 */
router.post('/ad-accounts', [
  body('accessToken').notEmpty().withMessage('Access token is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { accessToken } = req.body;

  try {
    // Check rate limit
    await facebookService.checkRateLimit('public_user');

    // Fetch ad accounts from Facebook API
    const adAccounts = await facebookService.getAdAccounts(accessToken);

    logger.info('Public ad accounts retrieved', {
      adAccountsCount: adAccounts.length
    });

    res.json({
      success: true,
      data: adAccounts
    });
  } catch (error) {
    logger.error('Error fetching public ad accounts:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook-public/campaigns/{adAccountId}:
 *   post:
 *     summary: Get campaigns for an ad account (Public)
 *     tags: [Facebook Public]
 *     parameters:
 *       - in: path
 *         name: adAccountId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - accessToken
 *             properties:
 *               accessToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Campaigns retrieved successfully
 */
router.post('/campaigns/:adAccountId', [
  body('accessToken').notEmpty().withMessage('Access token is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { adAccountId } = req.params;
  const { accessToken } = req.body;

  try {
    // Check rate limit
    await facebookService.checkRateLimit('public_user');

    // Get campaigns from Facebook API
    const campaigns = await facebookService.getCampaigns(accessToken, adAccountId);

    logger.info('Public campaigns retrieved', {
      adAccountId,
      campaignsCount: campaigns.length
    });

    res.json({
      success: true,
      data: campaigns
    });
  } catch (error) {
    logger.error('Error fetching public campaigns:', error);
    throw error;
  }
}));

module.exports = router;
