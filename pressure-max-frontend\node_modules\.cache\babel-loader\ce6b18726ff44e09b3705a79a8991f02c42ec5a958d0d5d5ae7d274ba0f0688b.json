{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\AuthSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthSection = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Test localStorage on component mount\n  useEffect(() => {\n    console.log('AuthSection: Component mounted, testing localStorage');\n    try {\n      localStorage.setItem('test_key', 'test_value');\n      const testValue = localStorage.getItem('test_key');\n      localStorage.removeItem('test_key');\n      console.log('AuthSection: localStorage test successful', {\n        testValue\n      });\n    } catch (error) {\n      console.error('AuthSection: localStorage test failed', error);\n    }\n  }, []);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    console.log('AuthSection useEffect: Checking for OAuth callback', {\n      code: code ? 'present' : 'missing',\n      state: state ? state.substring(0, 8) + '...' : 'missing',\n      isAuthenticated,\n      currentUrl: window.location.href,\n      localStorageKeys: Object.keys(localStorage)\n    });\n    if (code && state && !isAuthenticated) {\n      console.log('AuthSection: Processing OAuth callback');\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n  const onLogin = async data => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      console.log('AuthSection: Requesting OAuth URL for redirectUri:', redirectUri);\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('AuthSection: OAuth URL response:', response.data);\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('AuthSection: Verified stored state:', storedState);\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n        console.log('AuthSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      console.error('AuthSection: Facebook login error:', error);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state,\n        localStorageKeys: Object.keys(localStorage),\n        currentUrl: window.location.href\n      });\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', {\n          receivedState: state,\n          storedState\n        });\n\n        // In development mode with demo token, skip client-side state verification\n        // since the backend handles this and uses a demo token anyway\n        if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {\n          console.warn('Development mode: Skipping client-side state verification');\n          toast.warning('Development mode: Proceeding without state verification');\n        } else {\n          setLoading(false);\n          toast.error('Authentication failed: Invalid state parameter. Please try again.');\n          // Clear the URL parameters to clean up the UI\n          window.history.replaceState({}, document.title, window.location.pathname);\n          return; // Don't proceed with the API call\n        }\n      }\n\n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n\n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-status authenticated\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Role: \", user.role]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), user.facebookConnected && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#1877f2'\n              },\n              children: \"\\u2705 Facebook Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"token-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"JWT Token:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowToken(!showToken),\n              className: \"toggle-token\",\n              children: showToken ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), showToken && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-display\",\n            children: /*#__PURE__*/_jsxDEV(\"code\", {\n              children: getToken()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), \"Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Authentication\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'facebook' ? 'active' : '',\n        onClick: () => setActiveTab('facebook'),\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), \"Facebook Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'login' ? 'active' : '',\n        onClick: () => setActiveTab('login'),\n        children: \"Email Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'register' ? 'active' : '',\n        onClick: () => setActiveTab('register'),\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-login-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"facebook-login-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Facebook, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), \"Login with Facebook\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect your Facebook Business Manager account to access:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Ad account management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Campaign creation and monitoring\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Lead generation forms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Marketing API permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 No separate email/password required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleFacebookLogin,\n        disabled: loading,\n        className: \"facebook-login-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"facebook-note\",\n        children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"***************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 55\n        }, this), \"This will automatically grant marketing API permissions.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: loginForm.handleSubmit(onLogin),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...loginForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...loginForm.register('password', {\n            required: 'Password is required'\n          }),\n          placeholder: \"Enter your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: registerForm.handleSubmit(onRegister),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"First Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('firstName', {\n            required: 'First name is required'\n          }),\n          placeholder: \"Enter your first name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.firstName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Last Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('lastName', {\n            required: 'Last name is required'\n          }),\n          placeholder: \"Enter your last name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.lastName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...registerForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...registerForm.register('password', {\n            required: 'Password is required',\n            minLength: {\n              value: 8,\n              message: 'Password must be at least 8 characters'\n            }\n          }),\n          placeholder: \"Enter your password (min 8 characters)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Phone (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"tel\",\n          ...registerForm.register('phone'),\n          placeholder: \"Enter your phone number\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Registering...' : 'Register'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthSection, \"PNDPJnWBZpvF+7hEBwv+kDbuij4=\", false, function () {\n  return [useAuth, useForm, useForm];\n});\n_c = AuthSection;\nexport default AuthSection;\nvar _c;\n$RefreshReg$(_c, \"AuthSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useForm", "facebookAPI", "User", "LogOut", "Eye", "Eye<PERSON>ff", "Facebook", "toast", "jsxDEV", "_jsxDEV", "AuthSection", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "activeTab", "setActiveTab", "showToken", "setShowToken", "loading", "setLoading", "loginForm", "registerForm", "console", "log", "localStorage", "setItem", "testValue", "getItem", "removeItem", "error", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "substring", "currentUrl", "href", "localStorageKeys", "Object", "keys", "handleFacebookCallback", "onLogin", "data", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "response", "getOAuthUrl", "oauthUrl", "storedState", "Error", "message", "receivedState", "match", "process", "env", "NODE_ENV", "hostname", "warn", "warning", "history", "replaceState", "document", "title", "pathname", "handleOAuthCallback", "tokens", "accessToken", "refreshToken", "JSON", "stringify", "reload", "success", "url", "URL", "searchParams", "delete", "toString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "lastName", "email", "role", "facebookConnected", "style", "color", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "password", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/AuthSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst AuthSection = () => {\n  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Test localStorage on component mount\n  useEffect(() => {\n    console.log('AuthSection: Component mounted, testing localStorage');\n    try {\n      localStorage.setItem('test_key', 'test_value');\n      const testValue = localStorage.getItem('test_key');\n      localStorage.removeItem('test_key');\n      console.log('AuthSection: localStorage test successful', { testValue });\n    } catch (error) {\n      console.error('AuthSection: localStorage test failed', error);\n    }\n  }, []);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n\n    console.log('AuthSection useEffect: Checking for OAuth callback', {\n      code: code ? 'present' : 'missing',\n      state: state ? state.substring(0, 8) + '...' : 'missing',\n      isAuthenticated,\n      currentUrl: window.location.href,\n      localStorageKeys: Object.keys(localStorage)\n    });\n\n    if (code && state && !isAuthenticated) {\n      console.log('AuthSection: Processing OAuth callback');\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      console.log('AuthSection: Requesting OAuth URL for redirectUri:', redirectUri);\n\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('AuthSection: OAuth URL response:', response.data);\n\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('AuthSection: Verified stored state:', storedState);\n\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n\n        console.log('AuthSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      console.error('AuthSection: Facebook login error:', error);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state,\n        localStorageKeys: Object.keys(localStorage),\n        currentUrl: window.location.href\n      });\n\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', { receivedState: state, storedState });\n\n        // In development mode with demo token, skip client-side state verification\n        // since the backend handles this and uses a demo token anyway\n        if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {\n          console.warn('Development mode: Skipping client-side state verification');\n          toast.warning('Development mode: Proceeding without state verification');\n        } else {\n          setLoading(false);\n          toast.error('Authentication failed: Invalid state parameter. Please try again.');\n          // Clear the URL parameters to clean up the UI\n          window.history.replaceState({}, document.title, window.location.pathname);\n          return; // Don't proceed with the API call\n        }\n      }\n      \n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      \n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      \n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        \n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n        \n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      \n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n\n  if (isAuthenticated) {\n    return (\n      <div className=\"auth-section\">\n        <h2>Authentication Status</h2>\n        <div className=\"auth-status authenticated\">\n          <div className=\"user-info\">\n            <User size={20} />\n            <div>\n              <p><strong>{user.firstName} {user.lastName}</strong></p>\n              <p>{user.email}</p>\n              <p>Role: {user.role}</p>\n              {user.facebookConnected && (\n                <p style={{color: '#1877f2'}}>✅ Facebook Connected</p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"token-section\">\n            <div className=\"token-header\">\n              <span>JWT Token:</span>\n              <button \n                onClick={() => setShowToken(!showToken)}\n                className=\"toggle-token\"\n              >\n                {showToken ? <EyeOff size={16} /> : <Eye size={16} />}\n              </button>\n            </div>\n            {showToken && (\n              <div className=\"token-display\">\n                <code>{getToken()}</code>\n              </div>\n            )}\n          </div>\n\n          <button onClick={logout} className=\"logout-btn\">\n            <LogOut size={16} />\n            Logout\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-section\">\n      <h2>Authentication</h2>\n      \n      <div className=\"auth-tabs\">\n        <button \n          className={activeTab === 'facebook' ? 'active' : ''}\n          onClick={() => setActiveTab('facebook')}\n        >\n          <Facebook size={16} />\n          Facebook Login\n        </button>\n        <button \n          className={activeTab === 'login' ? 'active' : ''}\n          onClick={() => setActiveTab('login')}\n        >\n          Email Login\n        </button>\n        <button \n          className={activeTab === 'register' ? 'active' : ''}\n          onClick={() => setActiveTab('register')}\n        >\n          Register\n        </button>\n      </div>\n\n      {activeTab === 'facebook' ? (\n        <div className=\"facebook-login-section\">\n          <div className=\"facebook-login-info\">\n            <h3>\n              <Facebook size={20} />\n              Login with Facebook\n            </h3>\n            <p>Connect your Facebook Business Manager account to access:</p>\n            <ul>\n              <li>✅ Ad account management</li>\n              <li>✅ Campaign creation and monitoring</li>\n              <li>✅ Lead generation forms</li>\n              <li>✅ Marketing API permissions</li>\n              <li>✅ No separate email/password required</li>\n            </ul>\n          </div>\n          \n          <button \n            onClick={handleFacebookLogin}\n            disabled={loading}\n            className=\"facebook-login-btn\"\n          >\n            <Facebook size={20} />\n            {loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager'}\n          </button>\n          \n          <p className=\"facebook-note\">\n            Using App ID: <code>***************</code><br/>\n            This will automatically grant marketing API permissions.\n          </p>\n        </div>\n      ) : activeTab === 'login' ? (\n        <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...loginForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {loginForm.formState.errors.email && (\n              <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...loginForm.register('password', { required: 'Password is required' })}\n              placeholder=\"Enter your password\"\n            />\n            {loginForm.formState.errors.password && (\n              <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n      ) : (\n        <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>First Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('firstName', { required: 'First name is required' })}\n              placeholder=\"Enter your first name\"\n            />\n            {registerForm.formState.errors.firstName && (\n              <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Last Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('lastName', { required: 'Last name is required' })}\n              placeholder=\"Enter your last name\"\n            />\n            {registerForm.formState.errors.lastName && (\n              <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...registerForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {registerForm.formState.errors.email && (\n              <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...registerForm.register('password', { \n                required: 'Password is required',\n                minLength: { value: 8, message: 'Password must be at least 8 characters' }\n              })}\n              placeholder=\"Enter your password (min 8 characters)\"\n            />\n            {registerForm.formState.errors.password && (\n              <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Phone (optional):</label>\n            <input\n              type=\"tel\"\n              {...registerForm.register('phone')}\n              placeholder=\"Enter your phone number\"\n            />\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Registering...' : 'Register'}\n          </button>\n        </form>\n      )}\n    </div>\n  );\n};\n\nexport default AuthSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AAClE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC9E,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM2B,SAAS,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,YAAY,GAAGzB,OAAO,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd4B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnE,IAAI;MACFC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC;MAC9C,MAAMC,SAAS,GAAGF,YAAY,CAACG,OAAO,CAAC,UAAU,CAAC;MAClDH,YAAY,CAACI,UAAU,CAAC,UAAU,CAAC;MACnCN,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;QAAEG;MAAU,CAAC,CAAC;IACzE,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMoC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpCd,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;MAChEY,IAAI,EAAEA,IAAI,GAAG,SAAS,GAAG,SAAS;MAClCE,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,SAAS;MACxD7B,eAAe;MACf8B,UAAU,EAAEP,MAAM,CAACC,QAAQ,CAACO,IAAI;MAChCC,gBAAgB,EAAEC,MAAM,CAACC,IAAI,CAACnB,YAAY;IAC5C,CAAC,CAAC;IAEF,IAAIW,IAAI,IAAIE,KAAK,IAAI,CAAC5B,eAAe,EAAE;MACrCa,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDqB,sBAAsB,CAACT,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAAC5B,eAAe,CAAC,CAAC;EAErB,MAAMoC,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9B3B,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMT,KAAK,CAACoC,IAAI,CAAC;IACjB3B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM4B,UAAU,GAAG,MAAOD,IAAI,IAAK;IACjC3B,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMR,QAAQ,CAACmC,IAAI,CAAC;IACpB3B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM6B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF7B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM8B,WAAW,GAAG,GAAGjB,MAAM,CAACC,QAAQ,CAACiB,MAAM,GAAG;MAChD5B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE0B,WAAW,CAAC;MAE9E,MAAME,QAAQ,GAAG,MAAMtD,WAAW,CAACuD,WAAW,CAACH,WAAW,CAAC;MAC3D3B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE4B,QAAQ,CAACL,IAAI,CAAC;MAE9D,IAAIK,QAAQ,CAACL,IAAI,CAACO,QAAQ,IAAIF,QAAQ,CAACL,IAAI,CAACT,KAAK,EAAE;QACjD;QACAf,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE4B,QAAQ,CAACL,IAAI,CAACT,KAAK,CAAC;QACrE;QACAb,YAAY,CAACI,UAAU,CAAC,sBAAsB,CAAC;QAC/CJ,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAE0B,QAAQ,CAACL,IAAI,CAACT,KAAK,CAAC;;QAEjE;QACA,MAAMiB,WAAW,GAAG9B,YAAY,CAACG,OAAO,CAAC,sBAAsB,CAAC;QAChEL,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE+B,WAAW,CAAC;QAE/D,IAAIA,WAAW,KAAKH,QAAQ,CAACL,IAAI,CAACT,KAAK,EAAE;UACvC,MAAM,IAAIkB,KAAK,CAAC,uCAAuC,CAAC;QAC1D;QAEAjC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7DS,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAGW,QAAQ,CAACL,IAAI,CAACO,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,4CAA4C,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdV,UAAU,CAAC,KAAK,CAAC;MACjBG,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D1B,KAAK,CAAC0B,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAAC2B,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMZ,sBAAsB,GAAG,MAAAA,CAAOT,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMmC,WAAW,GAAG9B,YAAY,CAACG,OAAO,CAAC,sBAAsB,CAAC;MAChEL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCkC,aAAa,EAAEpB,KAAK;QACpBiB,WAAW;QACXI,KAAK,EAAEJ,WAAW,KAAKjB,KAAK;QAC5BI,gBAAgB,EAAEC,MAAM,CAACC,IAAI,CAACnB,YAAY,CAAC;QAC3Ce,UAAU,EAAEP,MAAM,CAACC,QAAQ,CAACO;MAC9B,CAAC,CAAC;MAEF,IAAI,CAACc,WAAW,IAAIA,WAAW,KAAKjB,KAAK,EAAE;QACzCf,OAAO,CAACO,KAAK,CAAC,2BAA2B,EAAE;UAAE4B,aAAa,EAAEpB,KAAK;UAAEiB;QAAY,CAAC,CAAC;;QAEjF;QACA;QACA,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI7B,MAAM,CAACC,QAAQ,CAAC6B,QAAQ,KAAK,WAAW,EAAE;UACtFxC,OAAO,CAACyC,IAAI,CAAC,2DAA2D,CAAC;UACzE5D,KAAK,CAAC6D,OAAO,CAAC,yDAAyD,CAAC;QAC1E,CAAC,MAAM;UACL7C,UAAU,CAAC,KAAK,CAAC;UACjBhB,KAAK,CAAC0B,KAAK,CAAC,mEAAmE,CAAC;UAChF;UACAG,MAAM,CAACiC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEpC,MAAM,CAACC,QAAQ,CAACoC,QAAQ,CAAC;UACzE,OAAO,CAAC;QACV;MACF;;MAEA;MACA7C,YAAY,CAACI,UAAU,CAAC,sBAAsB,CAAC;MAE/C,MAAMqB,WAAW,GAAG,GAAGjB,MAAM,CAACC,QAAQ,CAACiB,MAAM,GAAG;MAChD,MAAMC,QAAQ,GAAG,MAAMtD,WAAW,CAACyE,mBAAmB,CAACnC,IAAI,EAAEE,KAAK,EAAEY,WAAW,CAAC;MAEhF,IAAIE,QAAQ,CAACL,IAAI,CAACtC,IAAI,IAAI2C,QAAQ,CAACL,IAAI,CAACyB,MAAM,EAAE;QAC9C;QACA/C,YAAY,CAACC,OAAO,CAAC,aAAa,EAAE0B,QAAQ,CAACL,IAAI,CAACyB,MAAM,CAACC,WAAW,CAAC;QACrEhD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE0B,QAAQ,CAACL,IAAI,CAACyB,MAAM,CAACE,YAAY,CAAC;QACvEjD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEiD,IAAI,CAACC,SAAS,CAACxB,QAAQ,CAACL,IAAI,CAACtC,IAAI,CAAC,CAAC;;QAEhE;QACAwB,MAAM,CAACC,QAAQ,CAAC2C,MAAM,CAAC,CAAC,CAAC,CAAC;;QAE1BzE,KAAK,CAAC0E,OAAO,CAAC,uCAAuC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdV,UAAU,CAAC,KAAK,CAAC;MACjBhB,KAAK,CAAC0B,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAAC2B,OAAO,CAAC;;MAEtD;MACA,MAAMsB,GAAG,GAAG,IAAIC,GAAG,CAAC/C,MAAM,CAACC,QAAQ,CAAC;MACpC6C,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;MAC/BH,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;MAChCjD,MAAM,CAACiC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEU,GAAG,CAACI,QAAQ,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;EAED,IAAIzE,eAAe,EAAE;IACnB,oBACEJ,OAAA;MAAK8E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B/E,OAAA;QAAA+E,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BnF,OAAA;QAAK8E,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC/E,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/E,OAAA,CAACP,IAAI;YAAC2F,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClBnF,OAAA;YAAA+E,QAAA,gBACE/E,OAAA;cAAA+E,QAAA,eAAG/E,OAAA;gBAAA+E,QAAA,GAAS5E,IAAI,CAACkF,SAAS,EAAC,GAAC,EAAClF,IAAI,CAACmF,QAAQ;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDnF,OAAA;cAAA+E,QAAA,EAAI5E,IAAI,CAACoF;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBnF,OAAA;cAAA+E,QAAA,GAAG,QAAM,EAAC5E,IAAI,CAACqF,IAAI;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvBhF,IAAI,CAACsF,iBAAiB,iBACrBzF,OAAA;cAAG0F,KAAK,EAAE;gBAACC,KAAK,EAAE;cAAS,CAAE;cAAAZ,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/E,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAA+E,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBnF,OAAA;cACE4F,OAAO,EAAEA,CAAA,KAAMhF,YAAY,CAAC,CAACD,SAAS,CAAE;cACxCmE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAEvBpE,SAAS,gBAAGX,OAAA,CAACJ,MAAM;gBAACwF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnF,OAAA,CAACL,GAAG;gBAACyF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLxE,SAAS,iBACRX,OAAA;YAAK8E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/E,OAAA;cAAA+E,QAAA,EAAOvE,QAAQ,CAAC;YAAC;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENnF,OAAA;UAAQ4F,OAAO,EAAErF,MAAO;UAACuE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC7C/E,OAAA,CAACN,MAAM;YAAC0F,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnF,OAAA;IAAK8E,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B/E,OAAA;MAAA+E,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvBnF,OAAA;MAAK8E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB/E,OAAA;QACE8E,SAAS,EAAErE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDmF,OAAO,EAAEA,CAAA,KAAMlF,YAAY,CAAC,UAAU,CAAE;QAAAqE,QAAA,gBAExC/E,OAAA,CAACH,QAAQ;UAACuF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnF,OAAA;QACE8E,SAAS,EAAErE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;QACjDmF,OAAO,EAAEA,CAAA,KAAMlF,YAAY,CAAC,OAAO,CAAE;QAAAqE,QAAA,EACtC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnF,OAAA;QACE8E,SAAS,EAAErE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDmF,OAAO,EAAEA,CAAA,KAAMlF,YAAY,CAAC,UAAU,CAAE;QAAAqE,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL1E,SAAS,KAAK,UAAU,gBACvBT,OAAA;MAAK8E,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrC/E,OAAA;QAAK8E,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC/E,OAAA;UAAA+E,QAAA,gBACE/E,OAAA,CAACH,QAAQ;YAACuF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnF,OAAA;UAAA+E,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEnF,OAAA;UAAA+E,QAAA,gBACE/E,OAAA;YAAA+E,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCnF,OAAA;YAAA+E,QAAA,EAAI;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CnF,OAAA;YAAA+E,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCnF,OAAA;YAAA+E,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCnF,OAAA;YAAA+E,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENnF,OAAA;QACE4F,OAAO,EAAEjD,mBAAoB;QAC7BkD,QAAQ,EAAEhF,OAAQ;QAClBiE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAE9B/E,OAAA,CAACH,QAAQ;UAACuF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBtE,OAAO,GAAG,2BAA2B,GAAG,sCAAsC;MAAA;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAETnF,OAAA;QAAG8E,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,gBACb,eAAA/E,OAAA;UAAA+E,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAAAnF,OAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,4DAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,GACJ1E,SAAS,KAAK,OAAO,gBACvBT,OAAA;MAAM8F,QAAQ,EAAE/E,SAAS,CAACgF,YAAY,CAACvD,OAAO,CAAE;MAACsC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpE/E,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAA+E,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBnF,OAAA;UACEgG,IAAI,EAAC,OAAO;UAAA,GACRjF,SAAS,CAACT,QAAQ,CAAC,OAAO,EAAE;YAAE2F,QAAQ,EAAE;UAAoB,CAAC,CAAC;UAClEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDpE,SAAS,CAACoF,SAAS,CAACC,MAAM,CAACb,KAAK,iBAC/BvF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEhE,SAAS,CAACoF,SAAS,CAACC,MAAM,CAACb,KAAK,CAACpC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAA+E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBnF,OAAA;UACEgG,IAAI,EAAC,UAAU;UAAA,GACXjF,SAAS,CAACT,QAAQ,CAAC,UAAU,EAAE;YAAE2F,QAAQ,EAAE;UAAuB,CAAC,CAAC;UACxEC,WAAW,EAAC;QAAqB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDpE,SAAS,CAACoF,SAAS,CAACC,MAAM,CAACC,QAAQ,iBAClCrG,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEhE,SAAS,CAACoF,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAClD;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnF,OAAA;QAAQgG,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAEhF,OAAQ;QAACiE,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DlE,OAAO,GAAG,eAAe,GAAG;MAAO;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEPnF,OAAA;MAAM8F,QAAQ,EAAE9E,YAAY,CAAC+E,YAAY,CAACrD,UAAU,CAAE;MAACoC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC1E/E,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAA+E,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BnF,OAAA;UACEgG,IAAI,EAAC,MAAM;UAAA,GACPhF,YAAY,CAACV,QAAQ,CAAC,WAAW,EAAE;YAAE2F,QAAQ,EAAE;UAAyB,CAAC,CAAC;UAC9EC,WAAW,EAAC;QAAuB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDnE,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACf,SAAS,iBACtCrF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/D,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACf,SAAS,CAAClC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAA+E,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzBnF,OAAA;UACEgG,IAAI,EAAC,MAAM;UAAA,GACPhF,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YAAE2F,QAAQ,EAAE;UAAwB,CAAC,CAAC;UAC5EC,WAAW,EAAC;QAAsB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACDnE,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACd,QAAQ,iBACrCtF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/D,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACd,QAAQ,CAACnC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAA+E,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBnF,OAAA;UACEgG,IAAI,EAAC,OAAO;UAAA,GACRhF,YAAY,CAACV,QAAQ,CAAC,OAAO,EAAE;YAAE2F,QAAQ,EAAE;UAAoB,CAAC,CAAC;UACrEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDnE,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACb,KAAK,iBAClCvF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/D,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACb,KAAK,CAACpC;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAA+E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBnF,OAAA;UACEgG,IAAI,EAAC,UAAU;UAAA,GACXhF,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YACpC2F,QAAQ,EAAE,sBAAsB;YAChCK,SAAS,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAEpD,OAAO,EAAE;YAAyC;UAC3E,CAAC,CAAC;UACF+C,WAAW,EAAC;QAAwC;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EACDnE,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACC,QAAQ,iBACrCrG,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE/D,YAAY,CAACmF,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAClD;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAA+E,QAAA,EAAO;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCnF,OAAA;UACEgG,IAAI,EAAC,KAAK;UAAA,GACNhF,YAAY,CAACV,QAAQ,CAAC,OAAO,CAAC;UAClC4F,WAAW,EAAC;QAAyB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnF,OAAA;QAAQgG,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAEhF,OAAQ;QAACiE,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DlE,OAAO,GAAG,gBAAgB,GAAG;MAAU;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CA7VID,WAAW;EAAA,QACsDX,OAAO,EAK1DC,OAAO,EACJA,OAAO;AAAA;AAAAiH,EAAA,GAPxBvG,WAAW;AA+VjB,eAAeA,WAAW;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}