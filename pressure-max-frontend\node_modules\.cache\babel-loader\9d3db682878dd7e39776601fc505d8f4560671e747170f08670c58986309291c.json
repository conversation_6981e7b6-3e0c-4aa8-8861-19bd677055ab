{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\FacebookSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport { Facebook, Users, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FacebookSection = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const [facebookStatus, setFacebookStatus] = useState({\n    connected: false,\n    loading: false,\n    accounts: [],\n    pages: [],\n    error: null\n  });\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadFacebookData();\n    }\n  }, [isAuthenticated]);\n  const loadFacebookData = async () => {\n    try {\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: true,\n        error: null\n      }));\n      const [accountsResponse, pagesResponse] = await Promise.all([facebookAPI.getAdAccounts().catch(() => ({\n        data: []\n      })), facebookAPI.getPages().catch(() => ({\n        data: []\n      }))]);\n      setFacebookStatus(prev => ({\n        ...prev,\n        connected: accountsResponse.data.length > 0 || pagesResponse.data.length > 0,\n        accounts: accountsResponse.data || [],\n        pages: pagesResponse.data || [],\n        loading: false\n      }));\n    } catch (error) {\n      setFacebookStatus(prev => {\n        var _error$response, _error$response$data;\n        return {\n          ...prev,\n          loading: false,\n          error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to load Facebook data'\n        };\n      });\n    }\n  };\n  const initiateOAuth = async () => {\n    try {\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: true\n      }));\n      const redirectUri = `${window.location.origin}/`;\n      console.log('FacebookSection: Requesting OAuth URL for redirectUri:', redirectUri);\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('FacebookSection: OAuth URL response:', response.data);\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('FacebookSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('FacebookSection: Verified stored state:', storedState);\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n        console.log('FacebookSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: false\n      }));\n      console.error('FacebookSection: OAuth initiation error:', error);\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to initiate Facebook OAuth');\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Facebook Integration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please log in to connect your Facebook account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"facebook-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Facebook Integration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-header\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Connection Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), facebookStatus.connected ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 16,\n          className: \"status-icon connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 16,\n          className: \"status-icon disconnected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `status-text ${facebookStatus.connected ? 'connected' : 'disconnected'}`,\n        children: facebookStatus.connected ? 'Connected to Facebook' : 'Not connected to Facebook'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), facebookStatus.error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: facebookStatus.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), !facebookStatus.connected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"oauth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Connect your Facebook Business Manager account to start managing campaigns.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: initiateOAuth,\n        disabled: facebookStatus.loading,\n        className: \"oauth-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), facebookStatus.loading ? 'Connecting...' : 'Connect Facebook Account']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this), facebookStatus.connected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-data\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"data-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this), \"Ad Accounts (\", facebookStatus.accounts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), facebookStatus.accounts.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"accounts-list\",\n          children: facebookStatus.accounts.map(account => {\n            // Convert numeric account status to readable string\n            const getAccountStatus = status => {\n              switch (status) {\n                case 1:\n                  return {\n                    text: 'ACTIVE',\n                    class: 'active'\n                  };\n                case 2:\n                  return {\n                    text: 'DISABLED',\n                    class: 'disabled'\n                  };\n                case 3:\n                  return {\n                    text: 'UNSETTLED',\n                    class: 'unsettled'\n                  };\n                case 7:\n                  return {\n                    text: 'PENDING_RISK_REVIEW',\n                    class: 'pending'\n                  };\n                case 8:\n                  return {\n                    text: 'PENDING_SETTLEMENT',\n                    class: 'pending'\n                  };\n                case 9:\n                  return {\n                    text: 'IN_GRACE_PERIOD',\n                    class: 'grace'\n                  };\n                case 100:\n                  return {\n                    text: 'PENDING_CLOSURE',\n                    class: 'pending'\n                  };\n                case 101:\n                  return {\n                    text: 'CLOSED',\n                    class: 'closed'\n                  };\n                case 201:\n                  return {\n                    text: 'ANY_ACTIVE',\n                    class: 'active'\n                  };\n                case 202:\n                  return {\n                    text: 'ANY_CLOSED',\n                    class: 'closed'\n                  };\n                default:\n                  return {\n                    text: `STATUS_${status}`,\n                    class: 'unknown'\n                  };\n              }\n            };\n            const statusInfo = getAccountStatus(account.account_status);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"account-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"account-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: account.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"account-id\",\n                  children: [\"ID: \", account.account_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `account-status ${statusInfo.class}`,\n                  children: statusInfo.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"account-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Currency: \", account.currency]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Timezone: \", account.timezone_name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 23\n              }, this)]\n            }, account.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 21\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-data\",\n          children: \"No ad accounts found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"data-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Users, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), \"Pages (\", facebookStatus.pages.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this), facebookStatus.pages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pages-list\",\n          children: facebookStatus.pages.map(page => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-item\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"page-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: page.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"page-id\",\n                children: [\"ID: \", page.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"page-category\",\n                children: page.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 21\n            }, this)\n          }, page.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-data\",\n          children: \"No pages found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadFacebookData,\n        disabled: facebookStatus.loading,\n        className: \"refresh-btn\",\n        children: facebookStatus.loading ? 'Refreshing...' : 'Refresh Data'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(FacebookSection, \"0I4X/jUwT1nYIG7AOx1EYPhrKO0=\", false, function () {\n  return [useAuth];\n});\n_c = FacebookSection;\nexport default FacebookSection;\nvar _c;\n$RefreshReg$(_c, \"FacebookSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "facebookAPI", "useAuth", "toast", "Facebook", "Users", "CreditCard", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "FacebookSection", "_s", "isAuthenticated", "facebookStatus", "setFacebookStatus", "connected", "loading", "accounts", "pages", "error", "loadFacebookData", "prev", "accountsResponse", "pagesResponse", "Promise", "all", "getAdAccounts", "catch", "data", "getPages", "length", "_error$response", "_error$response$data", "response", "message", "<PERSON><PERSON><PERSON>", "redirectUri", "window", "location", "origin", "console", "log", "getOAuthUrl", "oauthUrl", "state", "localStorage", "removeItem", "setItem", "storedState", "getItem", "Error", "href", "_error$response2", "_error$response2$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "disabled", "map", "account", "getAccountStatus", "status", "text", "class", "statusInfo", "account_status", "name", "account_id", "currency", "timezone_name", "id", "page", "category", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/FacebookSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport { Facebook, Users, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';\n\nconst FacebookSection = () => {\n  const { isAuthenticated } = useAuth();\n  const [facebookStatus, setFacebookStatus] = useState({\n    connected: false,\n    loading: false,\n    accounts: [],\n    pages: [],\n    error: null\n  });\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadFacebookData();\n    }\n  }, [isAuthenticated]);\n\n  const loadFacebookData = async () => {\n    try {\n      setFacebookStatus(prev => ({ ...prev, loading: true, error: null }));\n      \n      const [accountsResponse, pagesResponse] = await Promise.all([\n        facebookAPI.getAdAccounts().catch(() => ({ data: [] })),\n        facebookAPI.getPages().catch(() => ({ data: [] }))\n      ]);\n\n      setFacebookStatus(prev => ({\n        ...prev,\n        connected: accountsResponse.data.length > 0 || pagesResponse.data.length > 0,\n        accounts: accountsResponse.data || [],\n        pages: pagesResponse.data || [],\n        loading: false\n      }));\n    } catch (error) {\n      setFacebookStatus(prev => ({\n        ...prev,\n        loading: false,\n        error: error.response?.data?.message || 'Failed to load Facebook data'\n      }));\n    }\n  };\n\n  const initiateOAuth = async () => {\n    try {\n      setFacebookStatus(prev => ({ ...prev, loading: true }));\n\n      const redirectUri = `${window.location.origin}/`;\n      console.log('FacebookSection: Requesting OAuth URL for redirectUri:', redirectUri);\n\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('FacebookSection: OAuth URL response:', response.data);\n\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('FacebookSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('FacebookSection: Verified stored state:', storedState);\n\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n\n        console.log('FacebookSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      setFacebookStatus(prev => ({ ...prev, loading: false }));\n      console.error('FacebookSection: OAuth initiation error:', error);\n      toast.error(error.response?.data?.message || 'Failed to initiate Facebook OAuth');\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"facebook-section\">\n        <h2>Facebook Integration</h2>\n        <div className=\"auth-required\">\n          <AlertCircle size={20} />\n          <p>Please log in to connect your Facebook account</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"facebook-section\">\n      <h2>Facebook Integration</h2>\n      \n      <div className=\"facebook-status\">\n        <div className=\"status-header\">\n          <Facebook size={20} />\n          <span>Connection Status</span>\n          {facebookStatus.connected ? (\n            <CheckCircle size={16} className=\"status-icon connected\" />\n          ) : (\n            <AlertCircle size={16} className=\"status-icon disconnected\" />\n          )}\n        </div>\n        \n        <p className={`status-text ${facebookStatus.connected ? 'connected' : 'disconnected'}`}>\n          {facebookStatus.connected ? 'Connected to Facebook' : 'Not connected to Facebook'}\n        </p>\n\n        {facebookStatus.error && (\n          <div className=\"error-message\">\n            <AlertCircle size={16} />\n            <span>{facebookStatus.error}</span>\n          </div>\n        )}\n      </div>\n\n      {!facebookStatus.connected && (\n        <div className=\"oauth-section\">\n          <p>Connect your Facebook Business Manager account to start managing campaigns.</p>\n          <button \n            onClick={initiateOAuth}\n            disabled={facebookStatus.loading}\n            className=\"oauth-btn\"\n          >\n            <Facebook size={16} />\n            {facebookStatus.loading ? 'Connecting...' : 'Connect Facebook Account'}\n          </button>\n        </div>\n      )}\n\n      {facebookStatus.connected && (\n        <div className=\"facebook-data\">\n          <div className=\"data-section\">\n            <h3>\n              <CreditCard size={16} />\n              Ad Accounts ({facebookStatus.accounts.length})\n            </h3>\n            {facebookStatus.accounts.length > 0 ? (\n              <div className=\"accounts-list\">\n                {facebookStatus.accounts.map((account) => {\n                  // Convert numeric account status to readable string\n                  const getAccountStatus = (status) => {\n                    switch(status) {\n                      case 1: return { text: 'ACTIVE', class: 'active' };\n                      case 2: return { text: 'DISABLED', class: 'disabled' };\n                      case 3: return { text: 'UNSETTLED', class: 'unsettled' };\n                      case 7: return { text: 'PENDING_RISK_REVIEW', class: 'pending' };\n                      case 8: return { text: 'PENDING_SETTLEMENT', class: 'pending' };\n                      case 9: return { text: 'IN_GRACE_PERIOD', class: 'grace' };\n                      case 100: return { text: 'PENDING_CLOSURE', class: 'pending' };\n                      case 101: return { text: 'CLOSED', class: 'closed' };\n                      case 201: return { text: 'ANY_ACTIVE', class: 'active' };\n                      case 202: return { text: 'ANY_CLOSED', class: 'closed' };\n                      default: return { text: `STATUS_${status}`, class: 'unknown' };\n                    }\n                  };\n\n                  const statusInfo = getAccountStatus(account.account_status);\n\n                  return (\n                    <div key={account.id} className=\"account-item\">\n                      <div className=\"account-info\">\n                        <strong>{account.name}</strong>\n                        <span className=\"account-id\">ID: {account.account_id}</span>\n                        <span className={`account-status ${statusInfo.class}`}>\n                          {statusInfo.text}\n                        </span>\n                      </div>\n                      <div className=\"account-details\">\n                        <span>Currency: {account.currency}</span>\n                        <span>Timezone: {account.timezone_name}</span>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            ) : (\n              <p className=\"no-data\">No ad accounts found</p>\n            )}\n          </div>\n\n          <div className=\"data-section\">\n            <h3>\n              <Users size={16} />\n              Pages ({facebookStatus.pages.length})\n            </h3>\n            {facebookStatus.pages.length > 0 ? (\n              <div className=\"pages-list\">\n                {facebookStatus.pages.map((page) => (\n                  <div key={page.id} className=\"page-item\">\n                    <div className=\"page-info\">\n                      <strong>{page.name}</strong>\n                      <span className=\"page-id\">ID: {page.id}</span>\n                      <span className=\"page-category\">{page.category}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"no-data\">No pages found</p>\n            )}\n          </div>\n\n          <button \n            onClick={loadFacebookData}\n            disabled={facebookStatus.loading}\n            className=\"refresh-btn\"\n          >\n            {facebookStatus.loading ? 'Refreshing...' : 'Refresh Data'}\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FacebookSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EACrC,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC;IACnDiB,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACd,IAAIa,eAAe,EAAE;MACnBQ,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EAErB,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFN,iBAAiB,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,OAAO,EAAE,IAAI;QAAEG,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;MAEpE,MAAM,CAACG,gBAAgB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DzB,WAAW,CAAC0B,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC,EACvD5B,WAAW,CAAC6B,QAAQ,CAAC,CAAC,CAACF,KAAK,CAAC,OAAO;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC,CACnD,CAAC;MAEFd,iBAAiB,CAACO,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPN,SAAS,EAAEO,gBAAgB,CAACM,IAAI,CAACE,MAAM,GAAG,CAAC,IAAIP,aAAa,CAACK,IAAI,CAACE,MAAM,GAAG,CAAC;QAC5Eb,QAAQ,EAAEK,gBAAgB,CAACM,IAAI,IAAI,EAAE;QACrCV,KAAK,EAAEK,aAAa,CAACK,IAAI,IAAI,EAAE;QAC/BZ,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdL,iBAAiB,CAACO,IAAI;QAAA,IAAAU,eAAA,EAAAC,oBAAA;QAAA,OAAK;UACzB,GAAGX,IAAI;UACPL,OAAO,EAAE,KAAK;UACdG,KAAK,EAAE,EAAAY,eAAA,GAAAZ,KAAK,CAACc,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI;QAC1C,CAAC;MAAA,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFrB,iBAAiB,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MAEvD,MAAMoB,WAAW,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG;MAChDC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEL,WAAW,CAAC;MAElF,MAAMH,QAAQ,GAAG,MAAMjC,WAAW,CAAC0C,WAAW,CAACN,WAAW,CAAC;MAC3DI,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAER,QAAQ,CAACL,IAAI,CAAC;MAElE,IAAIK,QAAQ,CAACL,IAAI,CAACe,QAAQ,IAAIV,QAAQ,CAACL,IAAI,CAACgB,KAAK,EAAE;QACjD;QACAJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAER,QAAQ,CAACL,IAAI,CAACgB,KAAK,CAAC;QACzE;QACAC,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;QAC/CD,YAAY,CAACE,OAAO,CAAC,sBAAsB,EAAEd,QAAQ,CAACL,IAAI,CAACgB,KAAK,CAAC;;QAEjE;QACA,MAAMI,WAAW,GAAGH,YAAY,CAACI,OAAO,CAAC,sBAAsB,CAAC;QAChET,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEO,WAAW,CAAC;QAEnE,IAAIA,WAAW,KAAKf,QAAQ,CAACL,IAAI,CAACgB,KAAK,EAAE;UACvC,MAAM,IAAIM,KAAK,CAAC,uCAAuC,CAAC;QAC1D;QAEAV,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjEJ,MAAM,CAACC,QAAQ,CAACa,IAAI,GAAGlB,QAAQ,CAACL,IAAI,CAACe,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIO,KAAK,CAAC,4CAA4C,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACdvC,iBAAiB,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;MACxDwB,OAAO,CAACrB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEjB,KAAK,CAACiB,KAAK,CAAC,EAAAiC,gBAAA,GAAAjC,KAAK,CAACc,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,mCAAmC,CAAC;IACnF;EACF,CAAC;EAED,IAAI,CAACtB,eAAe,EAAE;IACpB,oBACEH,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B9C,OAAA;QAAA8C,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BlD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9C,OAAA,CAACH,WAAW;UAACsD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzBlD,OAAA;UAAA8C,QAAA,EAAG;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElD,OAAA;IAAK6C,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B9C,OAAA;MAAA8C,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE7BlD,OAAA;MAAK6C,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9C,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9C,OAAA,CAACN,QAAQ;UAACyD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtBlD,OAAA;UAAA8C,QAAA,EAAM;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC7B9C,cAAc,CAACE,SAAS,gBACvBN,OAAA,CAACF,WAAW;UAACqD,IAAI,EAAE,EAAG;UAACN,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3DlD,OAAA,CAACH,WAAW;UAACsD,IAAI,EAAE,EAAG;UAACN,SAAS,EAAC;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC9D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlD,OAAA;QAAG6C,SAAS,EAAE,eAAezC,cAAc,CAACE,SAAS,GAAG,WAAW,GAAG,cAAc,EAAG;QAAAwC,QAAA,EACpF1C,cAAc,CAACE,SAAS,GAAG,uBAAuB,GAAG;MAA2B;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,EAEH9C,cAAc,CAACM,KAAK,iBACnBV,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9C,OAAA,CAACH,WAAW;UAACsD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzBlD,OAAA;UAAA8C,QAAA,EAAO1C,cAAc,CAACM;QAAK;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAAC9C,cAAc,CAACE,SAAS,iBACxBN,OAAA;MAAK6C,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B9C,OAAA;QAAA8C,QAAA,EAAG;MAA2E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClFlD,OAAA;QACEoD,OAAO,EAAE1B,aAAc;QACvB2B,QAAQ,EAAEjD,cAAc,CAACG,OAAQ;QACjCsC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAErB9C,OAAA,CAACN,QAAQ;UAACyD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrB9C,cAAc,CAACG,OAAO,GAAG,eAAe,GAAG,0BAA0B;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAEA9C,cAAc,CAACE,SAAS,iBACvBN,OAAA;MAAK6C,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B9C,OAAA;QAAK6C,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9C,OAAA;UAAA8C,QAAA,gBACE9C,OAAA,CAACJ,UAAU;YAACuD,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBACX,EAAC9C,cAAc,CAACI,QAAQ,CAACa,MAAM,EAAC,GAC/C;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJ9C,cAAc,CAACI,QAAQ,CAACa,MAAM,GAAG,CAAC,gBACjCrB,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B1C,cAAc,CAACI,QAAQ,CAAC8C,GAAG,CAAEC,OAAO,IAAK;YACxC;YACA,MAAMC,gBAAgB,GAAIC,MAAM,IAAK;cACnC,QAAOA,MAAM;gBACX,KAAK,CAAC;kBAAE,OAAO;oBAAEC,IAAI,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBAClD,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,UAAU;oBAAEC,KAAK,EAAE;kBAAW,CAAC;gBACtD,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,WAAW;oBAAEC,KAAK,EAAE;kBAAY,CAAC;gBACxD,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,qBAAqB;oBAAEC,KAAK,EAAE;kBAAU,CAAC;gBAChE,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,oBAAoB;oBAAEC,KAAK,EAAE;kBAAU,CAAC;gBAC/D,KAAK,CAAC;kBAAE,OAAO;oBAAED,IAAI,EAAE,iBAAiB;oBAAEC,KAAK,EAAE;kBAAQ,CAAC;gBAC1D,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,iBAAiB;oBAAEC,KAAK,EAAE;kBAAU,CAAC;gBAC9D,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBACpD,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,YAAY;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBACxD,KAAK,GAAG;kBAAE,OAAO;oBAAED,IAAI,EAAE,YAAY;oBAAEC,KAAK,EAAE;kBAAS,CAAC;gBACxD;kBAAS,OAAO;oBAAED,IAAI,EAAE,UAAUD,MAAM,EAAE;oBAAEE,KAAK,EAAE;kBAAU,CAAC;cAChE;YACF,CAAC;YAED,MAAMC,UAAU,GAAGJ,gBAAgB,CAACD,OAAO,CAACM,cAAc,CAAC;YAE3D,oBACE7D,OAAA;cAAsB6C,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC5C9C,OAAA;gBAAK6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B9C,OAAA;kBAAA8C,QAAA,EAASS,OAAO,CAACO;gBAAI;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAC/BlD,OAAA;kBAAM6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,MAAI,EAACS,OAAO,CAACQ,UAAU;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DlD,OAAA;kBAAM6C,SAAS,EAAE,kBAAkBe,UAAU,CAACD,KAAK,EAAG;kBAAAb,QAAA,EACnDc,UAAU,CAACF;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B9C,OAAA;kBAAA8C,QAAA,GAAM,YAAU,EAACS,OAAO,CAACS,QAAQ;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzClD,OAAA;kBAAA8C,QAAA,GAAM,YAAU,EAACS,OAAO,CAACU,aAAa;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA,GAXEK,OAAO,CAACW,EAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYf,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENlD,OAAA;UAAG6C,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC/C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlD,OAAA;QAAK6C,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9C,OAAA;UAAA8C,QAAA,gBACE9C,OAAA,CAACL,KAAK;YAACwD,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WACZ,EAAC9C,cAAc,CAACK,KAAK,CAACY,MAAM,EAAC,GACtC;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJ9C,cAAc,CAACK,KAAK,CAACY,MAAM,GAAG,CAAC,gBAC9BrB,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB1C,cAAc,CAACK,KAAK,CAAC6C,GAAG,CAAEa,IAAI,iBAC7BnE,OAAA;YAAmB6C,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtC9C,OAAA;cAAK6C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9C,OAAA;gBAAA8C,QAAA,EAASqB,IAAI,CAACL;cAAI;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC5BlD,OAAA;gBAAM6C,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAC,MAAI,EAACqB,IAAI,CAACD,EAAE;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ClD,OAAA;gBAAM6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEqB,IAAI,CAACC;cAAQ;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC,GALEiB,IAAI,CAACD,EAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENlD,OAAA;UAAG6C,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlD,OAAA;QACEoD,OAAO,EAAEzC,gBAAiB;QAC1B0C,QAAQ,EAAEjD,cAAc,CAACG,OAAQ;QACjCsC,SAAS,EAAC,aAAa;QAAAC,QAAA,EAEtB1C,cAAc,CAACG,OAAO,GAAG,eAAe,GAAG;MAAc;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChD,EAAA,CAvNID,eAAe;EAAA,QACST,OAAO;AAAA;AAAA6E,EAAA,GAD/BpE,eAAe;AAyNrB,eAAeA,eAAe;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}