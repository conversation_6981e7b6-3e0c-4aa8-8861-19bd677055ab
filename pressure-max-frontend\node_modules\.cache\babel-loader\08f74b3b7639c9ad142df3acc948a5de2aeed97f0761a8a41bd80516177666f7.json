{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\AuthSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthSection = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [oauthProcessing, setOauthProcessing] = useState(false);\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Test localStorage on component mount\n  useEffect(() => {\n    console.log('AuthSection: Component mounted, testing localStorage');\n    try {\n      localStorage.setItem('test_key', 'test_value');\n      const testValue = localStorage.getItem('test_key');\n      localStorage.removeItem('test_key');\n      console.log('AuthSection: localStorage test successful', {\n        testValue\n      });\n    } catch (error) {\n      console.error('AuthSection: localStorage test failed', error);\n    }\n  }, []);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    console.log('AuthSection useEffect: Checking for OAuth callback', {\n      code: code ? 'present' : 'missing',\n      state: state ? state.substring(0, 8) + '...' : 'missing',\n      isAuthenticated,\n      oauthProcessing,\n      currentUrl: window.location.href,\n      localStorageKeys: Object.keys(localStorage)\n    });\n    if (code && state && !isAuthenticated && !oauthProcessing) {\n      console.log('AuthSection: Processing OAuth callback');\n      setOauthProcessing(true);\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated, oauthProcessing]);\n  const onLogin = async data => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      console.log('AuthSection: Requesting OAuth URL for redirectUri:', redirectUri);\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('AuthSection: OAuth URL response:', response.data);\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('AuthSection: Verified stored state:', storedState);\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n        console.log('AuthSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      console.error('AuthSection: Facebook login error:', error);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state,\n        localStorageKeys: Object.keys(localStorage),\n        currentUrl: window.location.href\n      });\n\n      // Clean up stored state immediately to prevent double processing\n      if (storedState) {\n        localStorage.removeItem('facebook_oauth_state');\n      }\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', {\n          receivedState: state,\n          storedState\n        });\n\n        // In development mode with demo token, skip client-side state verification\n        // since the backend handles this and uses a demo token anyway\n        if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {\n          console.warn('Development mode: Skipping client-side state verification');\n          toast.warning('Development mode: Proceeding without state verification');\n        } else {\n          setLoading(false);\n          toast.error('Authentication failed: Invalid state parameter. Please try again.');\n          // Clear the URL parameters to clean up the UI\n          window.history.replaceState({}, document.title, window.location.pathname);\n          return; // Don't proceed with the API call\n        }\n      }\n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.success && response.data.profile) {\n        // Store the access token and profile data\n        if (response.data.accessToken) {\n          localStorage.setItem('facebook_access_token', response.data.accessToken);\n        }\n        localStorage.setItem('facebook_profile', JSON.stringify(response.data.profile));\n\n        // Clear URL parameters to clean up the UI\n        window.history.replaceState({}, document.title, window.location.pathname);\n        toast.success(`Successfully connected Facebook account for ${response.data.profile.name}!`);\n        setLoading(false);\n        setOauthProcessing(false);\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      setOauthProcessing(false);\n      console.error('Facebook callback error:', error);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clear URL parameters to clean up the UI\n      window.history.replaceState({}, document.title, window.location.pathname);\n    }\n  };\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-status authenticated\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Role: \", user.role]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), user.facebookConnected && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#1877f2'\n              },\n              children: \"\\u2705 Facebook Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"token-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"JWT Token:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowToken(!showToken),\n              className: \"toggle-token\",\n              children: showToken ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), showToken && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-display\",\n            children: /*#__PURE__*/_jsxDEV(\"code\", {\n              children: getToken()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), \"Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Authentication\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'facebook' ? 'active' : '',\n        onClick: () => setActiveTab('facebook'),\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), \"Facebook Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'login' ? 'active' : '',\n        onClick: () => setActiveTab('login'),\n        children: \"Email Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'register' ? 'active' : '',\n        onClick: () => setActiveTab('register'),\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-login-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"facebook-login-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Facebook, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), \"Login with Facebook\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect your Facebook Business Manager account to access:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Ad account management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Campaign creation and monitoring\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Lead generation forms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Marketing API permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 No separate email/password required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleFacebookLogin,\n        disabled: loading,\n        className: \"facebook-login-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"facebook-note\",\n        children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"***************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 55\n        }, this), \"This will automatically grant marketing API permissions.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 9\n    }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: loginForm.handleSubmit(onLogin),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...loginForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...loginForm.register('password', {\n            required: 'Password is required'\n          }),\n          placeholder: \"Enter your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: registerForm.handleSubmit(onRegister),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"First Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('firstName', {\n            required: 'First name is required'\n          }),\n          placeholder: \"Enter your first name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.firstName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Last Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('lastName', {\n            required: 'Last name is required'\n          }),\n          placeholder: \"Enter your last name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.lastName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...registerForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...registerForm.register('password', {\n            required: 'Password is required',\n            minLength: {\n              value: 8,\n              message: 'Password must be at least 8 characters'\n            }\n          }),\n          placeholder: \"Enter your password (min 8 characters)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Phone (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"tel\",\n          ...registerForm.register('phone'),\n          placeholder: \"Enter your phone number\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Registering...' : 'Register'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthSection, \"nsv2WIPoGIYFRxM+yc7tVTI5XfM=\", false, function () {\n  return [useAuth, useForm, useForm];\n});\n_c = AuthSection;\nexport default AuthSection;\nvar _c;\n$RefreshReg$(_c, \"AuthSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useForm", "facebookAPI", "User", "LogOut", "Eye", "Eye<PERSON>ff", "Facebook", "toast", "jsxDEV", "_jsxDEV", "AuthSection", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "activeTab", "setActiveTab", "showToken", "setShowToken", "loading", "setLoading", "oauthProcessing", "setOauthProcessing", "loginForm", "registerForm", "console", "log", "localStorage", "setItem", "testValue", "getItem", "removeItem", "error", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "substring", "currentUrl", "href", "localStorageKeys", "Object", "keys", "handleFacebookCallback", "onLogin", "data", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "response", "getOAuthUrl", "oauthUrl", "storedState", "Error", "message", "receivedState", "match", "process", "env", "NODE_ENV", "hostname", "warn", "warning", "history", "replaceState", "document", "title", "pathname", "handleOAuthCallback", "success", "profile", "accessToken", "JSON", "stringify", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "lastName", "email", "role", "facebookConnected", "style", "color", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "password", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/AuthSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst AuthSection = () => {\n  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [oauthProcessing, setOauthProcessing] = useState(false);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Test localStorage on component mount\n  useEffect(() => {\n    console.log('AuthSection: Component mounted, testing localStorage');\n    try {\n      localStorage.setItem('test_key', 'test_value');\n      const testValue = localStorage.getItem('test_key');\n      localStorage.removeItem('test_key');\n      console.log('AuthSection: localStorage test successful', { testValue });\n    } catch (error) {\n      console.error('AuthSection: localStorage test failed', error);\n    }\n  }, []);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n\n    console.log('AuthSection useEffect: Checking for OAuth callback', {\n      code: code ? 'present' : 'missing',\n      state: state ? state.substring(0, 8) + '...' : 'missing',\n      isAuthenticated,\n      oauthProcessing,\n      currentUrl: window.location.href,\n      localStorageKeys: Object.keys(localStorage)\n    });\n\n    if (code && state && !isAuthenticated && !oauthProcessing) {\n      console.log('AuthSection: Processing OAuth callback');\n      setOauthProcessing(true);\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated, oauthProcessing]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      console.log('AuthSection: Requesting OAuth URL for redirectUri:', redirectUri);\n\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      console.log('AuthSection: OAuth URL response:', response.data);\n\n      if (response.data.oauthUrl && response.data.state) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n\n        // Verify storage worked\n        const storedState = localStorage.getItem('facebook_oauth_state');\n        console.log('AuthSection: Verified stored state:', storedState);\n\n        if (storedState !== response.data.state) {\n          throw new Error('Failed to store state in localStorage');\n        }\n\n        console.log('AuthSection: Redirecting to Facebook OAuth URL');\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL or state received from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      console.error('AuthSection: Facebook login error:', error);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state,\n        localStorageKeys: Object.keys(localStorage),\n        currentUrl: window.location.href\n      });\n\n      // Clean up stored state immediately to prevent double processing\n      if (storedState) {\n        localStorage.removeItem('facebook_oauth_state');\n      }\n\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', { receivedState: state, storedState });\n\n        // In development mode with demo token, skip client-side state verification\n        // since the backend handles this and uses a demo token anyway\n        if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {\n          console.warn('Development mode: Skipping client-side state verification');\n          toast.warning('Development mode: Proceeding without state verification');\n        } else {\n          setLoading(false);\n          toast.error('Authentication failed: Invalid state parameter. Please try again.');\n          // Clear the URL parameters to clean up the UI\n          window.history.replaceState({}, document.title, window.location.pathname);\n          return; // Don't proceed with the API call\n        }\n      }\n\n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n\n      if (response.data.success && response.data.profile) {\n        // Store the access token and profile data\n        if (response.data.accessToken) {\n          localStorage.setItem('facebook_access_token', response.data.accessToken);\n        }\n        localStorage.setItem('facebook_profile', JSON.stringify(response.data.profile));\n\n        // Clear URL parameters to clean up the UI\n        window.history.replaceState({}, document.title, window.location.pathname);\n\n        toast.success(`Successfully connected Facebook account for ${response.data.profile.name}!`);\n        setLoading(false);\n        setOauthProcessing(false);\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error) {\n      setLoading(false);\n      setOauthProcessing(false);\n      console.error('Facebook callback error:', error);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clear URL parameters to clean up the UI\n      window.history.replaceState({}, document.title, window.location.pathname);\n    }\n  };\n\n  if (isAuthenticated) {\n    return (\n      <div className=\"auth-section\">\n        <h2>Authentication Status</h2>\n        <div className=\"auth-status authenticated\">\n          <div className=\"user-info\">\n            <User size={20} />\n            <div>\n              <p><strong>{user.firstName} {user.lastName}</strong></p>\n              <p>{user.email}</p>\n              <p>Role: {user.role}</p>\n              {user.facebookConnected && (\n                <p style={{color: '#1877f2'}}>✅ Facebook Connected</p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"token-section\">\n            <div className=\"token-header\">\n              <span>JWT Token:</span>\n              <button \n                onClick={() => setShowToken(!showToken)}\n                className=\"toggle-token\"\n              >\n                {showToken ? <EyeOff size={16} /> : <Eye size={16} />}\n              </button>\n            </div>\n            {showToken && (\n              <div className=\"token-display\">\n                <code>{getToken()}</code>\n              </div>\n            )}\n          </div>\n\n          <button onClick={logout} className=\"logout-btn\">\n            <LogOut size={16} />\n            Logout\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-section\">\n      <h2>Authentication</h2>\n      \n      <div className=\"auth-tabs\">\n        <button \n          className={activeTab === 'facebook' ? 'active' : ''}\n          onClick={() => setActiveTab('facebook')}\n        >\n          <Facebook size={16} />\n          Facebook Login\n        </button>\n        <button \n          className={activeTab === 'login' ? 'active' : ''}\n          onClick={() => setActiveTab('login')}\n        >\n          Email Login\n        </button>\n        <button \n          className={activeTab === 'register' ? 'active' : ''}\n          onClick={() => setActiveTab('register')}\n        >\n          Register\n        </button>\n      </div>\n\n      {activeTab === 'facebook' ? (\n        <div className=\"facebook-login-section\">\n          <div className=\"facebook-login-info\">\n            <h3>\n              <Facebook size={20} />\n              Login with Facebook\n            </h3>\n            <p>Connect your Facebook Business Manager account to access:</p>\n            <ul>\n              <li>✅ Ad account management</li>\n              <li>✅ Campaign creation and monitoring</li>\n              <li>✅ Lead generation forms</li>\n              <li>✅ Marketing API permissions</li>\n              <li>✅ No separate email/password required</li>\n            </ul>\n          </div>\n          \n          <button \n            onClick={handleFacebookLogin}\n            disabled={loading}\n            className=\"facebook-login-btn\"\n          >\n            <Facebook size={20} />\n            {loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager'}\n          </button>\n          \n          <p className=\"facebook-note\">\n            Using App ID: <code>***************</code><br/>\n            This will automatically grant marketing API permissions.\n          </p>\n        </div>\n      ) : activeTab === 'login' ? (\n        <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...loginForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {loginForm.formState.errors.email && (\n              <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...loginForm.register('password', { required: 'Password is required' })}\n              placeholder=\"Enter your password\"\n            />\n            {loginForm.formState.errors.password && (\n              <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n      ) : (\n        <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>First Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('firstName', { required: 'First name is required' })}\n              placeholder=\"Enter your first name\"\n            />\n            {registerForm.formState.errors.firstName && (\n              <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Last Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('lastName', { required: 'Last name is required' })}\n              placeholder=\"Enter your last name\"\n            />\n            {registerForm.formState.errors.lastName && (\n              <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...registerForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {registerForm.formState.errors.email && (\n              <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...registerForm.register('password', { \n                required: 'Password is required',\n                minLength: { value: 8, message: 'Password must be at least 8 characters' }\n              })}\n              placeholder=\"Enter your password (min 8 characters)\"\n            />\n            {registerForm.formState.errors.password && (\n              <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Phone (optional):</label>\n            <input\n              type=\"tel\"\n              {...registerForm.register('phone')}\n              placeholder=\"Enter your phone number\"\n            />\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Registering...' : 'Register'}\n          </button>\n        </form>\n      )}\n    </div>\n  );\n};\n\nexport default AuthSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AAClE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC9E,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAM6B,SAAS,GAAG1B,OAAO,CAAC,CAAC;EAC3B,MAAM2B,YAAY,GAAG3B,OAAO,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd8B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnE,IAAI;MACFC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC;MAC9C,MAAMC,SAAS,GAAGF,YAAY,CAACG,OAAO,CAAC,UAAU,CAAC;MAClDH,YAAY,CAACI,UAAU,CAAC,UAAU,CAAC;MACnCN,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;QAAEG;MAAU,CAAC,CAAC;IACzE,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArC,SAAS,CAAC,MAAM;IACd,MAAMsC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpCd,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;MAChEY,IAAI,EAAEA,IAAI,GAAG,SAAS,GAAG,SAAS;MAClCE,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,SAAS;MACxD/B,eAAe;MACfW,eAAe;MACfqB,UAAU,EAAEP,MAAM,CAACC,QAAQ,CAACO,IAAI;MAChCC,gBAAgB,EAAEC,MAAM,CAACC,IAAI,CAACnB,YAAY;IAC5C,CAAC,CAAC;IAEF,IAAIW,IAAI,IAAIE,KAAK,IAAI,CAAC9B,eAAe,IAAI,CAACW,eAAe,EAAE;MACzDI,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDJ,kBAAkB,CAAC,IAAI,CAAC;MACxByB,sBAAsB,CAACT,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAAC9B,eAAe,EAAEW,eAAe,CAAC,CAAC;EAEtC,MAAM2B,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9B7B,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMT,KAAK,CAACsC,IAAI,CAAC;IACjB7B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM8B,UAAU,GAAG,MAAOD,IAAI,IAAK;IACjC7B,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMR,QAAQ,CAACqC,IAAI,CAAC;IACpB7B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM+B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgC,WAAW,GAAG,GAAGjB,MAAM,CAACC,QAAQ,CAACiB,MAAM,GAAG;MAChD5B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE0B,WAAW,CAAC;MAE9E,MAAME,QAAQ,GAAG,MAAMxD,WAAW,CAACyD,WAAW,CAACH,WAAW,CAAC;MAC3D3B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE4B,QAAQ,CAACL,IAAI,CAAC;MAE9D,IAAIK,QAAQ,CAACL,IAAI,CAACO,QAAQ,IAAIF,QAAQ,CAACL,IAAI,CAACT,KAAK,EAAE;QACjD;QACAf,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE4B,QAAQ,CAACL,IAAI,CAACT,KAAK,CAAC;QACrE;QACAb,YAAY,CAACI,UAAU,CAAC,sBAAsB,CAAC;QAC/CJ,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAE0B,QAAQ,CAACL,IAAI,CAACT,KAAK,CAAC;;QAEjE;QACA,MAAMiB,WAAW,GAAG9B,YAAY,CAACG,OAAO,CAAC,sBAAsB,CAAC;QAChEL,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE+B,WAAW,CAAC;QAE/D,IAAIA,WAAW,KAAKH,QAAQ,CAACL,IAAI,CAACT,KAAK,EAAE;UACvC,MAAM,IAAIkB,KAAK,CAAC,uCAAuC,CAAC;QAC1D;QAEAjC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7DS,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAGW,QAAQ,CAACL,IAAI,CAACO,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,4CAA4C,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdZ,UAAU,CAAC,KAAK,CAAC;MACjBK,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D5B,KAAK,CAAC4B,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAAC2B,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMZ,sBAAsB,GAAG,MAAAA,CAAOT,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACFpB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMqC,WAAW,GAAG9B,YAAY,CAACG,OAAO,CAAC,sBAAsB,CAAC;MAChEL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCkC,aAAa,EAAEpB,KAAK;QACpBiB,WAAW;QACXI,KAAK,EAAEJ,WAAW,KAAKjB,KAAK;QAC5BI,gBAAgB,EAAEC,MAAM,CAACC,IAAI,CAACnB,YAAY,CAAC;QAC3Ce,UAAU,EAAEP,MAAM,CAACC,QAAQ,CAACO;MAC9B,CAAC,CAAC;;MAEF;MACA,IAAIc,WAAW,EAAE;QACf9B,YAAY,CAACI,UAAU,CAAC,sBAAsB,CAAC;MACjD;MAEA,IAAI,CAAC0B,WAAW,IAAIA,WAAW,KAAKjB,KAAK,EAAE;QACzCf,OAAO,CAACO,KAAK,CAAC,2BAA2B,EAAE;UAAE4B,aAAa,EAAEpB,KAAK;UAAEiB;QAAY,CAAC,CAAC;;QAEjF;QACA;QACA,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI7B,MAAM,CAACC,QAAQ,CAAC6B,QAAQ,KAAK,WAAW,EAAE;UACtFxC,OAAO,CAACyC,IAAI,CAAC,2DAA2D,CAAC;UACzE9D,KAAK,CAAC+D,OAAO,CAAC,yDAAyD,CAAC;QAC1E,CAAC,MAAM;UACL/C,UAAU,CAAC,KAAK,CAAC;UACjBhB,KAAK,CAAC4B,KAAK,CAAC,mEAAmE,CAAC;UAChF;UACAG,MAAM,CAACiC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEpC,MAAM,CAACC,QAAQ,CAACoC,QAAQ,CAAC;UACzE,OAAO,CAAC;QACV;MACF;MAEA,MAAMpB,WAAW,GAAG,GAAGjB,MAAM,CAACC,QAAQ,CAACiB,MAAM,GAAG;MAChD,MAAMC,QAAQ,GAAG,MAAMxD,WAAW,CAAC2E,mBAAmB,CAACnC,IAAI,EAAEE,KAAK,EAAEY,WAAW,CAAC;MAEhF,IAAIE,QAAQ,CAACL,IAAI,CAACyB,OAAO,IAAIpB,QAAQ,CAACL,IAAI,CAAC0B,OAAO,EAAE;QAClD;QACA,IAAIrB,QAAQ,CAACL,IAAI,CAAC2B,WAAW,EAAE;UAC7BjD,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAE0B,QAAQ,CAACL,IAAI,CAAC2B,WAAW,CAAC;QAC1E;QACAjD,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEiD,IAAI,CAACC,SAAS,CAACxB,QAAQ,CAACL,IAAI,CAAC0B,OAAO,CAAC,CAAC;;QAE/E;QACAxC,MAAM,CAACiC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEpC,MAAM,CAACC,QAAQ,CAACoC,QAAQ,CAAC;QAEzEpE,KAAK,CAACsE,OAAO,CAAC,+CAA+CpB,QAAQ,CAACL,IAAI,CAAC0B,OAAO,CAACI,IAAI,GAAG,CAAC;QAC3F3D,UAAU,CAAC,KAAK,CAAC;QACjBE,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIoC,KAAK,CAAC,8BAA8B,CAAC;MACjD;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdZ,UAAU,CAAC,KAAK,CAAC;MACjBE,kBAAkB,CAAC,KAAK,CAAC;MACzBG,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD5B,KAAK,CAAC4B,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAAC2B,OAAO,CAAC;;MAEtD;MACAxB,MAAM,CAACiC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEpC,MAAM,CAACC,QAAQ,CAACoC,QAAQ,CAAC;IAC3E;EACF,CAAC;EAED,IAAI9D,eAAe,EAAE;IACnB,oBACEJ,OAAA;MAAK0E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B3E,OAAA;QAAA2E,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B/E,OAAA;QAAK0E,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC3E,OAAA;UAAK0E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3E,OAAA,CAACP,IAAI;YAACuF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClB/E,OAAA;YAAA2E,QAAA,gBACE3E,OAAA;cAAA2E,QAAA,eAAG3E,OAAA;gBAAA2E,QAAA,GAASxE,IAAI,CAAC8E,SAAS,EAAC,GAAC,EAAC9E,IAAI,CAAC+E,QAAQ;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxD/E,OAAA;cAAA2E,QAAA,EAAIxE,IAAI,CAACgF;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB/E,OAAA;cAAA2E,QAAA,GAAG,QAAM,EAACxE,IAAI,CAACiF,IAAI;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvB5E,IAAI,CAACkF,iBAAiB,iBACrBrF,OAAA;cAAGsF,KAAK,EAAE;gBAACC,KAAK,EAAE;cAAS,CAAE;cAAAZ,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/E,OAAA;UAAK0E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3E,OAAA;YAAK0E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3E,OAAA;cAAA2E,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB/E,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAM5E,YAAY,CAAC,CAACD,SAAS,CAAE;cACxC+D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAEvBhE,SAAS,gBAAGX,OAAA,CAACJ,MAAM;gBAACoF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG/E,OAAA,CAACL,GAAG;gBAACqF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLpE,SAAS,iBACRX,OAAA;YAAK0E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B3E,OAAA;cAAA2E,QAAA,EAAOnE,QAAQ,CAAC;YAAC;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN/E,OAAA;UAAQwF,OAAO,EAAEjF,MAAO;UAACmE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC7C3E,OAAA,CAACN,MAAM;YAACsF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/E,OAAA;IAAK0E,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B3E,OAAA;MAAA2E,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvB/E,OAAA;MAAK0E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB3E,OAAA;QACE0E,SAAS,EAAEjE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpD+E,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,UAAU,CAAE;QAAAiE,QAAA,gBAExC3E,OAAA,CAACH,QAAQ;UAACmF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/E,OAAA;QACE0E,SAAS,EAAEjE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;QACjD+E,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,OAAO,CAAE;QAAAiE,QAAA,EACtC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/E,OAAA;QACE0E,SAAS,EAAEjE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpD+E,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,UAAU,CAAE;QAAAiE,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELtE,SAAS,KAAK,UAAU,gBACvBT,OAAA;MAAK0E,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrC3E,OAAA;QAAK0E,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC3E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA,CAACH,QAAQ;YAACmF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/E,OAAA;UAAA2E,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChE/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAA2E,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC/E,OAAA;YAAA2E,QAAA,EAAI;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3C/E,OAAA;YAAA2E,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC/E,OAAA;YAAA2E,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpC/E,OAAA;YAAA2E,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN/E,OAAA;QACEwF,OAAO,EAAE3C,mBAAoB;QAC7B4C,QAAQ,EAAE5E,OAAQ;QAClB6D,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAE9B3E,OAAA,CAACH,QAAQ;UAACmF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBlE,OAAO,GAAG,2BAA2B,GAAG,sCAAsC;MAAA;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAET/E,OAAA;QAAG0E,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,gBACb,eAAA3E,OAAA;UAAA2E,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAAA/E,OAAA;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,4DAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,GACJtE,SAAS,KAAK,OAAO,gBACvBT,OAAA;MAAM0F,QAAQ,EAAEzE,SAAS,CAAC0E,YAAY,CAACjD,OAAO,CAAE;MAACgC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpE3E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3E,OAAA;UAAA2E,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrB/E,OAAA;UACE4F,IAAI,EAAC,OAAO;UAAA,GACR3E,SAAS,CAACX,QAAQ,CAAC,OAAO,EAAE;YAAEuF,QAAQ,EAAE;UAAoB,CAAC,CAAC;UAClEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACD9D,SAAS,CAAC8E,SAAS,CAACC,MAAM,CAACb,KAAK,iBAC/BnF,OAAA;UAAM0E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE1D,SAAS,CAAC8E,SAAS,CAACC,MAAM,CAACb,KAAK,CAAC9B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3E,OAAA;UAAA2E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxB/E,OAAA;UACE4F,IAAI,EAAC,UAAU;UAAA,GACX3E,SAAS,CAACX,QAAQ,CAAC,UAAU,EAAE;YAAEuF,QAAQ,EAAE;UAAuB,CAAC,CAAC;UACxEC,WAAW,EAAC;QAAqB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACD9D,SAAS,CAAC8E,SAAS,CAACC,MAAM,CAACC,QAAQ,iBAClCjG,OAAA;UAAM0E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE1D,SAAS,CAAC8E,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAC5C;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA;QAAQ4F,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE5E,OAAQ;QAAC6D,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5D9D,OAAO,GAAG,eAAe,GAAG;MAAO;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEP/E,OAAA;MAAM0F,QAAQ,EAAExE,YAAY,CAACyE,YAAY,CAAC/C,UAAU,CAAE;MAAC8B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC1E3E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3E,OAAA;UAAA2E,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1B/E,OAAA;UACE4F,IAAI,EAAC,MAAM;UAAA,GACP1E,YAAY,CAACZ,QAAQ,CAAC,WAAW,EAAE;YAAEuF,QAAQ,EAAE;UAAyB,CAAC,CAAC;UAC9EC,WAAW,EAAC;QAAuB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACD7D,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACf,SAAS,iBACtCjF,OAAA;UAAM0E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEzD,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACf,SAAS,CAAC5B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3E,OAAA;UAAA2E,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB/E,OAAA;UACE4F,IAAI,EAAC,MAAM;UAAA,GACP1E,YAAY,CAACZ,QAAQ,CAAC,UAAU,EAAE;YAAEuF,QAAQ,EAAE;UAAwB,CAAC,CAAC;UAC5EC,WAAW,EAAC;QAAsB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACD7D,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACd,QAAQ,iBACrClF,OAAA;UAAM0E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEzD,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACd,QAAQ,CAAC7B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3E,OAAA;UAAA2E,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrB/E,OAAA;UACE4F,IAAI,EAAC,OAAO;UAAA,GACR1E,YAAY,CAACZ,QAAQ,CAAC,OAAO,EAAE;YAAEuF,QAAQ,EAAE;UAAoB,CAAC,CAAC;UACrEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACD7D,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACb,KAAK,iBAClCnF,OAAA;UAAM0E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEzD,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACb,KAAK,CAAC9B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3E,OAAA;UAAA2E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxB/E,OAAA;UACE4F,IAAI,EAAC,UAAU;UAAA,GACX1E,YAAY,CAACZ,QAAQ,CAAC,UAAU,EAAE;YACpCuF,QAAQ,EAAE,sBAAsB;YAChCK,SAAS,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAE9C,OAAO,EAAE;YAAyC;UAC3E,CAAC,CAAC;UACFyC,WAAW,EAAC;QAAwC;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EACD7D,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACC,QAAQ,iBACrCjG,OAAA;UAAM0E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEzD,YAAY,CAAC6E,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAC5C;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB3E,OAAA;UAAA2E,QAAA,EAAO;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChC/E,OAAA;UACE4F,IAAI,EAAC,KAAK;UAAA,GACN1E,YAAY,CAACZ,QAAQ,CAAC,OAAO,CAAC;UAClCwF,WAAW,EAAC;QAAyB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN/E,OAAA;QAAQ4F,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAE5E,OAAQ;QAAC6D,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5D9D,OAAO,GAAG,gBAAgB,GAAG;MAAU;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7E,EAAA,CAtWID,WAAW;EAAA,QACsDX,OAAO,EAM1DC,OAAO,EACJA,OAAO;AAAA;AAAA6G,EAAA,GARxBnG,WAAW;AAwWjB,eAAeA,WAAW;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}