/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.sections-container {
  display: grid;
  gap: 2rem;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.section h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #1e293b;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Button Styles */
button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.submit-btn {
  background: #667eea;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.oauth-btn {
  background: #1877f2;
  color: white;
}

.oauth-btn:hover:not(:disabled) {
  background: #166fe5;
}

.logout-btn {
  background: #ef4444;
  color: white;
}

.logout-btn:hover {
  background: #dc2626;
}

.test-btn {
  background: #10b981;
  color: white;
}

.test-btn:hover:not(:disabled) {
  background: #059669;
}

.clear-btn {
  background: #f59e0b;
  color: white;
}

.clear-btn:hover {
  background: #d97706;
}

.create-btn {
  background: #8b5cf6;
  color: white;
}

.create-btn:hover {
  background: #7c3aed;
}

.cancel-btn {
  background: #6b7280;
  color: white;
}

.cancel-btn:hover {
  background: #4b5563;
}

.refresh-btn {
  background: #06b6d4;
  color: white;
}

.refresh-btn:hover {
  background: #0891b2;
}

/* Auth Section */
.auth-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.auth-tabs button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-tabs button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

/* Facebook Login Section */
.facebook-login-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}

.facebook-login-info {
  margin-bottom: 2rem;
}

.facebook-login-info h3 {
  color: #1877f2;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.facebook-login-info ul {
  text-align: left;
  max-width: 400px;
  margin: 1rem auto;
  padding-left: 1rem;
}

.facebook-login-info li {
  margin-bottom: 0.5rem;
  color: #374151;
}

.facebook-login-btn {
  background: #1877f2;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 auto 1rem;
  transition: all 0.2s;
}

.facebook-login-btn:hover:not(:disabled) {
  background: #166fe5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
}

.facebook-login-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.facebook-note {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 1rem;
}

.facebook-note code {
  background: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.auth-status.authenticated {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 1.5rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.token-section {
  margin: 1rem 0;
}

.token-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.toggle-token {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  color: #6b7280;
}

.token-display {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.token-display code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  word-break: break-all;
  color: #1e293b;
}

/* Facebook Section */
.facebook-status {
  margin-bottom: 1.5rem;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-icon.connected {
  color: #10b981;
}

.status-icon.disconnected {
  color: #ef4444;
}

.status-text.connected {
  color: #10b981;
  font-weight: 500;
}

.status-text.disconnected {
  color: #ef4444;
  font-weight: 500;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 0.5rem;
}

.oauth-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.facebook-data {
  margin-top: 1.5rem;
}

.data-section {
  margin-bottom: 2rem;
}

.data-section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #374151;
}

.accounts-list,
.pages-list {
  display: grid;
  gap: 1rem;
}

.account-item,
.page-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
}

.account-info,
.page-info {
  margin-bottom: 0.5rem;
}

.account-info strong,
.page-info strong {
  display: block;
  margin-bottom: 0.25rem;
}

.account-id,
.page-id {
  font-size: 0.875rem;
  color: #6b7280;
  display: block;
}

.account-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  margin-top: 0.25rem;
}

.account-status.active {
  background: #dcfce7;
  color: #166534;
}

.account-status.disabled,
.account-status.closed {
  background: #fee2e2;
  color: #991b1b;
}

.account-status.pending,
.account-status.unsettled {
  background: #fef3c7;
  color: #92400e;
}

.account-status.grace {
  background: #dbeafe;
  color: #1e40af;
}

.account-status.unknown {
  background: #f3f4f6;
  color: #374151;
}

.account-details {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.page-category {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.no-data {
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

/* Campaign Section */
.account-selector {
  margin-bottom: 1.5rem;
}

.account-selector label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.account-selector select {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

/* Facebook Tabs */
.facebook-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.facebook-tabs button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.facebook-tabs button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.campaigns-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.campaigns-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.create-campaign-form {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.create-campaign-form h4 {
  margin-bottom: 1rem;
  color: #374151;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.campaigns-list {
  display: grid;
  gap: 1rem;
}

.campaign-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1.5rem;
}

.campaign-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.campaign-header h4 {
  margin: 0;
  color: #1e293b;
}

.campaign-status {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.campaign-status.active {
  background: #dcfce7;
  color: #166534;
}

.campaign-status.paused {
  background: #fef3c7;
  color: #92400e;
}

.campaign-details {
  display: grid;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.no-campaigns,
.no-adsets,
.no-ads {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

/* Ad Sets Section */
.adsets-section h3,
.ads-section h3 {
  margin-bottom: 1rem;
  color: #374151;
}

.adset-item,
.ad-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.adset-header,
.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.adset-header strong,
.ad-header strong {
  color: #374151;
  font-size: 1rem;
}

.adset-details,
.ad-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

/* API Testing Section */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.health-status {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.health-status.healthy {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.health-status.unhealthy {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.health-details p {
  margin-bottom: 0.25rem;
}

.profile-data {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.profile-item:last-child {
  border-bottom: none;
}

.test-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.logs-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.log-entry {
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.success {
  background: #f0fdf4;
}

.log-entry.error {
  background: #fef2f2;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.log-method {
  background: #374151;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
}

.log-endpoint {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1e293b;
}

.log-status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
}

.log-status.status-2 {
  background: #dcfce7;
  color: #166534;
}

.log-status.status-4,
.log-status.status-5,
.log-status.status-0 {
  background: #fecaca;
  color: #dc2626;
}

.log-timestamp {
  color: #6b7280;
  margin-left: auto;
}

.log-error {
  color: #dc2626;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.log-response {
  margin-top: 0.5rem;
}

.log-response summary {
  cursor: pointer;
  font-weight: 500;
  color: #374151;
}

.log-response pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 0.5rem;
  overflow-x: auto;
  font-size: 0.875rem;
}

.no-logs {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.auth-required,
.no-accounts,
.no-profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

/* Footer */
.app-footer {
  background: #1e293b;
  color: #94a3b8;
  text-align: center;
  padding: 1.5rem;
  margin-top: 2rem;
}

.app-footer p {
  margin-bottom: 0.5rem;
}

.app-footer code {
  background: #334155;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 1.5rem;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
  
  .app-main {
    padding: 1rem;
  }
  
  .section {
    padding: 1.5rem;
  }
  
  .campaigns-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .test-buttons {
    flex-direction: column;
  }
  
  .log-header {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .logs-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}

/* Targeting Display Styles */
.targeting-display {
  margin-top: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #f8fafc;
}

.targeting-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #f1f5f9;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  color: #475569;
  transition: background-color 0.2s;
}

.targeting-header:hover {
  background-color: #e2e8f0;
}

.targeting-details {
  padding: 1rem;
}

.targeting-section {
  margin-bottom: 1rem;
}

.targeting-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.section-content {
  margin-left: 1.25rem;
}

.targeting-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.targeting-item .label {
  font-weight: 500;
  color: #6b7280;
  min-width: 60px;
}

.targeting-item .value {
  color: #374151;
}

.interests-list,
.behaviors-list,
.platforms-list,
.placements-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.interest-tag,
.behavior-tag,
.platform-tag,
.placement-tag {
  background-color: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.behavior-tag {
  background-color: #fef3c7;
  color: #92400e;
}

.platform-tag {
  background-color: #d1fae5;
  color: #065f46;
}

.placement-tag {
  background-color: #ede9fe;
  color: #5b21b6;
}

.interest-tag.more,
.behavior-tag.more {
  background-color: #f3f4f6;
  color: #6b7280;
  font-style: italic;
}

/* Enhanced Targeting Display Styles */
.targeting-item .value.enabled {
  color: #059669;
  font-weight: 600;
}

.targeting-item .value.disabled {
  color: #dc2626;
  font-weight: 600;
}

/* Geographic Targeting Styles */
.geographic-subsection {
  margin-bottom: 0.75rem;
}

.subsection-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 0.375rem;
}

.subsection-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.geographic-tag {
  background-color: #d1fae5;
  color: #065f46;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid #a7f3d0;
}

/* Detailed Interest Styles */
.interests-detailed {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.interest-item {
  padding: 0.5rem;
  background-color: #fdf4ff;
  border-radius: 6px;
  border-left: 3px solid #a855f7;
}

.interest-name {
  font-weight: 600;
  color: #7c2d12;
  font-size: 0.8rem;
}

.interest-topic {
  margin-left: 0.5rem;
  font-size: 0.75rem;
  color: #a855f7;
  font-style: italic;
}

.interest-path {
  margin-top: 0.25rem;
  font-size: 0.7rem;
  color: #6b7280;
}

/* Detailed Behavior Styles */
.behaviors-detailed {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.behavior-item {
  padding: 0.5rem;
  background-color: #eff6ff;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.behavior-name {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.8rem;
}

.behavior-path {
  margin-top: 0.25rem;
  font-size: 0.7rem;
  color: #6b7280;
}

/* Enhanced Placement Styles */
.placement-subsection {
  margin-bottom: 0.75rem;
}

.placement-tag.platform {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.placement-tag.facebook {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #60a5fa;
}

.placement-tag.instagram {
  background-color: #fce7f3;
  color: #be185d;
  border: 1px solid #f472b6;
}

.placement-tag.messenger {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #34d399;
}

/* Automation Styles */
.automation-item {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background-color: #fef7ed;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
}

.automation-description {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}
