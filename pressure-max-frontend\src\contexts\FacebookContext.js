import React, { createContext, useContext, useState, useEffect } from 'react';
import { facebookPublicAPI } from '../services/api';
import toast from 'react-hot-toast';

const FacebookContext = createContext();

export const useFacebook = () => {
  const context = useContext(FacebookContext);
  if (!context) {
    throw new Error('useFacebook must be used within a FacebookProvider');
  }
  return context;
};

export const FacebookProvider = ({ children }) => {
  const [facebookUser, setFacebookUser] = useState(null);
  const [accessToken, setAccessToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    checkFacebookAuth();
    handleOAuthCallback();
  }, []);

  const checkFacebookAuth = () => {
    const token = localStorage.getItem('facebookAccessToken');
    const userData = localStorage.getItem('facebookUser');
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setAccessToken(token);
        setFacebookUser(parsedUser);
        setIsConnected(true);
      } catch (error) {
        console.error('Error parsing Facebook user data:', error);
        clearFacebookAuth();
      }
    }
    setLoading(false);
  };

  const handleOAuthCallback = async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    
    if (code && state) {
      try {
        setLoading(true);
        
        // Verify state parameter
        const storedState = localStorage.getItem('facebook_oauth_state');
        if (storedState !== state) {
          throw new Error('Invalid state parameter');
        }
        
        // Clean up stored state
        localStorage.removeItem('facebook_oauth_state');
        
        const redirectUri = `${window.location.origin}${window.location.pathname}`;
        const response = await facebookPublicAPI.handleOAuthCallback(code, state, redirectUri);
        
        if (response.data.success && response.data.accessToken) {
          // Store Facebook access token and user data
          localStorage.setItem('facebookAccessToken', response.data.accessToken);
          localStorage.setItem('facebookUser', JSON.stringify(response.data.profile));
          
          setAccessToken(response.data.accessToken);
          setFacebookUser(response.data.profile);
          setIsConnected(true);
          
          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);
          
          toast.success('Successfully connected to Facebook!');
        } else {
          throw new Error('No access token received');
        }
      } catch (error) {
        console.error('Facebook OAuth callback error:', error);
        toast.error('Failed to connect to Facebook: ' + error.message);
        clearFacebookAuth();
      } finally {
        setLoading(false);
      }
    }
  };

  const initiateFacebookLogin = async () => {
    try {
      setLoading(true);
      const redirectUri = `${window.location.origin}${window.location.pathname}`;
      const response = await facebookPublicAPI.getOAuthUrl(redirectUri);
      
      if (response.data.success && response.data.oauthUrl) {
        // Store state for verification
        localStorage.setItem('facebook_oauth_state', response.data.state);
        window.location.href = response.data.oauthUrl;
      } else {
        throw new Error('No OAuth URL received');
      }
    } catch (error) {
      setLoading(false);
      toast.error('Failed to initiate Facebook login: ' + error.message);
    }
  };

  const clearFacebookAuth = () => {
    localStorage.removeItem('facebookAccessToken');
    localStorage.removeItem('facebookUser');
    localStorage.removeItem('facebook_oauth_state');
    setAccessToken(null);
    setFacebookUser(null);
    setIsConnected(false);
  };

  const logout = () => {
    clearFacebookAuth();
    toast.success('Disconnected from Facebook');
  };

  const testConnection = async () => {
    if (!accessToken) {
      toast.error('No Facebook access token available');
      return false;
    }

    try {
      const response = await facebookPublicAPI.testToken(accessToken);
      if (response.data.success) {
        toast.success('Facebook connection is working!');
        return true;
      } else {
        throw new Error('Token test failed');
      }
    } catch (error) {
      toast.error('Facebook connection test failed: ' + error.message);
      return false;
    }
  };

  const value = {
    facebookUser,
    accessToken,
    loading,
    isConnected,
    initiateFacebookLogin,
    logout,
    testConnection,
  };

  return (
    <FacebookContext.Provider value={value}>
      {children}
    </FacebookContext.Provider>
  );
};
