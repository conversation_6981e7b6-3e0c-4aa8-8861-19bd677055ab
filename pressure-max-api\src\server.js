const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');

// Import configurations and middleware
const config = require('./config/config');
const logger = require('./config/logger');
const database = require('./config/database');
const redis = require('./config/redis');
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const facebookRoutes = require('./routes/facebook');
const facebookPublicRoutes = require('./routes/facebook-public');
const templateRoutes = require('./routes/templates');
const campaignRoutes = require('./routes/campaigns');
const vapiRoutes = require('./routes/vapi');
const leadRoutes = require('./routes/leads');
const webhookRoutes = require('./routes/webhooks');
const analyticsRoutes = require('./routes/analytics');

// Import services
const webhookService = require('./services/webhookService');
const notificationService = require('./services/notificationService');

class PressureMaxServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server, {
      cors: {
        origin: config.frontend.url,
        methods: ['GET', 'POST']
      }
    });
    this.port = config.server.port;
  }

  async initialize() {
    try {
      // Initialize database connection
      await database.initialize();
      logger.info('Database connected successfully');

      // Initialize Redis connection (optional for demo)
      try {
        await redis.initialize();
        logger.info('Redis connected successfully');
      } catch (error) {
        logger.warn('Redis connection failed, continuing without Redis:', error.message);
      }

      // Setup middleware
      this.setupMiddleware();

      // Setup routes
      this.setupRoutes();

      // Setup WebSocket handlers
      this.setupWebSocket();

      // Setup error handling
      this.setupErrorHandling();

      // Setup Swagger documentation
      this.setupSwagger();

      logger.info('Server initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize server:', error);
      process.exit(1);
    }
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: config.frontend.url,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Compression middleware
    this.app.use(compression());

    // Logging middleware
    this.app.use(morgan('combined', {
      stream: { write: message => logger.info(message.trim()) }
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.ceil(config.rateLimit.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0'
      });
    });
  }

  setupRoutes() {
    const apiRouter = express.Router();

    // Public routes (no authentication required)
    apiRouter.use('/auth', authRoutes);
    apiRouter.use('/webhooks', webhookRoutes);
    apiRouter.use('/facebook-public', facebookPublicRoutes);

    // Protected routes (authentication required)
    apiRouter.use('/users', authMiddleware.authenticate, userRoutes);
    apiRouter.use('/facebook', authMiddleware.authenticate, facebookRoutes);
    apiRouter.use('/templates', authMiddleware.authenticate, templateRoutes);
    apiRouter.use('/campaigns', authMiddleware.authenticate, campaignRoutes);
    apiRouter.use('/vapi', authMiddleware.authenticate, vapiRoutes);
    apiRouter.use('/leads', authMiddleware.authenticate, leadRoutes);
    apiRouter.use('/analytics', authMiddleware.authenticate, analyticsRoutes);

    // Mount API routes
    this.app.use(`/api/${config.api.version}`, apiRouter);

    // 404 handler for undefined routes
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Route not found',
        message: `The requested endpoint ${req.originalUrl} does not exist`
      });
    });
  }

  setupWebSocket() {
    // WebSocket authentication middleware (optional)
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          // Allow connection without auth for demo
          socket.userId = 'demo';
          socket.tenantId = 'demo';
          return next();
        }

        const decoded = authMiddleware.verifyToken(token);
        socket.userId = decoded.userId;
        socket.tenantId = decoded.tenantId;
        next();
      } catch (error) {
        // Allow connection without auth for demo
        socket.userId = 'demo';
        socket.tenantId = 'demo';
        next();
      }
    });

    // WebSocket connection handling
    this.io.on('connection', (socket) => {
      logger.info(`User ${socket.userId} connected via WebSocket`);

      // Join user-specific room
      socket.join(`user:${socket.userId}`);
      socket.join(`tenant:${socket.tenantId}`);

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info(`User ${socket.userId} disconnected from WebSocket`);
      });

      // Handle real-time events
      socket.on('subscribe:campaigns', (campaignIds) => {
        campaignIds.forEach(id => socket.join(`campaign:${id}`));
      });

      socket.on('subscribe:leads', () => {
        socket.join(`leads:${socket.tenantId}`);
      });
    });

    // Initialize notification service with WebSocket (if available)
    try {
      notificationService.initialize(this.io);
    } catch (error) {
      logger.warn('Notification service initialization failed:', error.message);
    }
  }

  setupErrorHandling() {
    // Global error handler
    this.app.use(errorHandler);

    // Unhandled promise rejection handler
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    // Uncaught exception handler
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      this.server.close(() => {
        logger.info('Process terminated');
        process.exit(0);
      });
    });
  }

  setupSwagger() {
    const options = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'Pressure Max API',
          version: '1.0.0',
          description: 'Comprehensive API for Facebook Ads automation and lead management platform',
          contact: {
            name: 'Pressure Max Team',
            email: '<EMAIL>'
          }
        },
        servers: [
          {
            url: `http://localhost:${this.port}/api/${config.api.version}`,
            description: 'Development server'
          }
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT'
            }
          }
        }
      },
      apis: ['./src/routes/*.js', './src/models/*.js']
    };

    const specs = swaggerJsdoc(options);
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));
  }

  async start() {
    await this.initialize();
    
    this.server.listen(this.port, () => {
      logger.info(`🚀 Pressure Max API server running on port ${this.port}`);
      logger.info(`📚 API Documentation available at http://localhost:${this.port}/api-docs`);
      logger.info(`🏥 Health check available at http://localhost:${this.port}/health`);
    });
  }
}

// Start the server
const server = new PressureMaxServer();
server.start().catch(error => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

module.exports = server;
