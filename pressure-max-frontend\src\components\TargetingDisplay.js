import React, { useState } from 'react';
import { ChevronDown, ChevronUp, MapPin, Users, Target, Smartphone, Eye, Heart } from 'lucide-react';

const TargetingDisplay = ({ targeting }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!targeting) {
    return (
      <div className="targeting-display">
        <div className="targeting-header">
          <Target size={16} />
          <span>No targeting data available</span>
        </div>
      </div>
    );
  }

  const hasTargetingData = targeting.demographics || targeting.geographic || 
                          targeting.interests?.length > 0 || targeting.behaviors?.length > 0 ||
                          targeting.custom_audiences?.length > 0 || targeting.lookalike_audiences?.length > 0;

  if (!hasTargetingData) {
    return (
      <div className="targeting-display">
        <div className="targeting-header">
          <Target size={16} />
          <span>Broad targeting (no specific criteria)</span>
        </div>
      </div>
    );
  }

  const formatAge = (demographics) => {
    if (!demographics) return null;
    const { age_min, age_max } = demographics;
    if (age_min && age_max) return `${age_min}-${age_max} years`;
    if (age_min) return `${age_min}+ years`;
    if (age_max) return `Up to ${age_max} years`;
    return null;
  };

  const formatGender = (genders) => {
    if (!genders || !Array.isArray(genders)) return null;
    const genderMap = { 1: 'Male', 2: 'Female', 3: 'All' };
    return genders.map(g => genderMap[g] || 'Unknown').join(', ');
  };

  const formatLocations = (geographic) => {
    if (!geographic) return null;
    const locations = [];
    if (geographic.countries) locations.push(`Countries: ${geographic.countries.join(', ')}`);
    if (geographic.regions) locations.push(`Regions: ${geographic.regions.length} selected`);
    if (geographic.cities) locations.push(`Cities: ${geographic.cities.length} selected`);
    return locations.length > 0 ? locations : null;
  };

  return (
    <div className="targeting-display">
      <div 
        className="targeting-header"
        onClick={() => setIsExpanded(!isExpanded)}
        style={{ cursor: 'pointer' }}
      >
        <Target size={16} />
        <span>Targeting Information</span>
        {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
      </div>

      {isExpanded && (
        <div className="targeting-details">
          {/* Demographics */}
          {targeting.demographics && (
            <div className="targeting-section">
              <div className="section-header">
                <Users size={14} />
                <span>Demographics</span>
              </div>
              <div className="section-content">
                {formatAge(targeting.demographics) && (
                  <div className="targeting-item">
                    <span className="label">Age:</span>
                    <span className="value">{formatAge(targeting.demographics)}</span>
                  </div>
                )}
                {targeting.demographics.genders && (
                  <div className="targeting-item">
                    <span className="label">Gender:</span>
                    <span className="value">{formatGender(targeting.demographics.genders)}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Geographic */}
          {targeting.geographic && formatLocations(targeting.geographic) && (
            <div className="targeting-section">
              <div className="section-header">
                <MapPin size={14} />
                <span>Geographic</span>
              </div>
              <div className="section-content">
                {formatLocations(targeting.geographic).map((location, index) => (
                  <div key={index} className="targeting-item">
                    <span className="value">{location}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Interests */}
          {targeting.interests && targeting.interests.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Heart size={14} />
                <span>Interests ({targeting.interests.length})</span>
              </div>
              <div className="section-content">
                <div className="interests-list">
                  {targeting.interests.slice(0, 5).map((interest, index) => (
                    <span key={index} className="interest-tag">
                      {interest.name || interest.id}
                    </span>
                  ))}
                  {targeting.interests.length > 5 && (
                    <span className="interest-tag more">
                      +{targeting.interests.length - 5} more
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Behaviors */}
          {targeting.behaviors && targeting.behaviors.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Eye size={14} />
                <span>Behaviors ({targeting.behaviors.length})</span>
              </div>
              <div className="section-content">
                <div className="behaviors-list">
                  {targeting.behaviors.slice(0, 3).map((behavior, index) => (
                    <span key={index} className="behavior-tag">
                      {behavior.name || behavior.id}
                    </span>
                  ))}
                  {targeting.behaviors.length > 3 && (
                    <span className="behavior-tag more">
                      +{targeting.behaviors.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Custom Audiences */}
          {targeting.custom_audiences && targeting.custom_audiences.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Users size={14} />
                <span>Custom Audiences ({targeting.custom_audiences.length})</span>
              </div>
              <div className="section-content">
                {targeting.custom_audiences.map((audience, index) => (
                  <div key={index} className="targeting-item">
                    <span className="value">{audience.name || audience.id}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Lookalike Audiences */}
          {targeting.lookalike_audiences && targeting.lookalike_audiences.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Users size={14} />
                <span>Lookalike Audiences ({targeting.lookalike_audiences.length})</span>
              </div>
              <div className="section-content">
                {targeting.lookalike_audiences.map((audience, index) => (
                  <div key={index} className="targeting-item">
                    <span className="value">
                      {audience.name || audience.id}
                      {audience.ratio && ` (${audience.ratio}%)`}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Device Platforms */}
          {targeting.device_platforms && targeting.device_platforms.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Smartphone size={14} />
                <span>Device Platforms</span>
              </div>
              <div className="section-content">
                <div className="platforms-list">
                  {targeting.device_platforms.map((platform, index) => (
                    <span key={index} className="platform-tag">
                      {platform}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Placements */}
          {targeting.placements && targeting.placements.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Eye size={14} />
                <span>Placements</span>
              </div>
              <div className="section-content">
                <div className="placements-list">
                  {targeting.placements.map((placement, index) => (
                    <span key={index} className="placement-tag">
                      {placement}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TargetingDisplay;
