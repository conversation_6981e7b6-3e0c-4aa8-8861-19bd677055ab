{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\AuthSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthSection = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n  const onLogin = async data => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      if (response.data.oauthUrl) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state\n      });\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', {\n          receivedState: state,\n          storedState\n        });\n        throw new Error('Invalid state parameter');\n      }\n\n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n\n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n\n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-status authenticated\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Role: \", user.role]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), user.facebookConnected && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#1877f2'\n              },\n              children: \"\\u2705 Facebook Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"token-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"JWT Token:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowToken(!showToken),\n              className: \"toggle-token\",\n              children: showToken ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), showToken && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"token-display\",\n            children: /*#__PURE__*/_jsxDEV(\"code\", {\n              children: getToken()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), \"Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Authentication\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'facebook' ? 'active' : '',\n        onClick: () => setActiveTab('facebook'),\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), \"Facebook Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'login' ? 'active' : '',\n        onClick: () => setActiveTab('login'),\n        children: \"Email Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'register' ? 'active' : '',\n        onClick: () => setActiveTab('register'),\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-login-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"facebook-login-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Facebook, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), \"Login with Facebook\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect your Facebook Business Manager account to access:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Ad account management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Campaign creation and monitoring\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Lead generation forms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Marketing API permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 No separate email/password required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleFacebookLogin,\n        disabled: loading,\n        className: \"facebook-login-btn\",\n        children: [/*#__PURE__*/_jsxDEV(Facebook, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"facebook-note\",\n        children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"***************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 55\n        }, this), \"This will automatically grant marketing API permissions.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: loginForm.handleSubmit(onLogin),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...loginForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...loginForm.register('password', {\n            required: 'Password is required'\n          }),\n          placeholder: \"Enter your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: loginForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: registerForm.handleSubmit(onRegister),\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"First Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('firstName', {\n            required: 'First name is required'\n          }),\n          placeholder: \"Enter your first name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.firstName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Last Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...registerForm.register('lastName', {\n            required: 'Last name is required'\n          }),\n          placeholder: \"Enter your last name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.lastName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...registerForm.register('email', {\n            required: 'Email is required'\n          }),\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Password:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...registerForm.register('password', {\n            required: 'Password is required',\n            minLength: {\n              value: 8,\n              message: 'Password must be at least 8 characters'\n            }\n          }),\n          placeholder: \"Enter your password (min 8 characters)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error\",\n          children: registerForm.formState.errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Phone (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"tel\",\n          ...registerForm.register('phone'),\n          placeholder: \"Enter your phone number\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"submit-btn\",\n        children: loading ? 'Registering...' : 'Register'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthSection, \"J+0be1bUuf2hGRBagTwIZhTtrCY=\", false, function () {\n  return [useAuth, useForm, useForm];\n});\n_c = AuthSection;\nexport default AuthSection;\nvar _c;\n$RefreshReg$(_c, \"AuthSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useForm", "facebookAPI", "User", "LogOut", "Eye", "Eye<PERSON>ff", "Facebook", "toast", "jsxDEV", "_jsxDEV", "AuthSection", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "activeTab", "setActiveTab", "showToken", "setShowToken", "loading", "setLoading", "loginForm", "registerForm", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "handleFacebookCallback", "onLogin", "data", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "response", "getOAuthUrl", "oauthUrl", "console", "log", "localStorage", "removeItem", "setItem", "href", "Error", "error", "message", "storedState", "getItem", "receivedState", "match", "handleOAuthCallback", "tokens", "accessToken", "refreshToken", "JSON", "stringify", "reload", "success", "url", "URL", "searchParams", "delete", "history", "replaceState", "document", "title", "toString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "lastName", "email", "role", "facebookConnected", "style", "color", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "password", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/AuthSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst AuthSection = () => {\n  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    \n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    await login(data);\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    await register(data);\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      \n      if (response.data.oauthUrl) {\n        // Store state for verification\n        console.log('AuthSection: Storing OAuth state:', response.data.state);\n        // Clear any existing state first\n        localStorage.removeItem('facebook_oauth_state');\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n\n      // Verify state parameter\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      console.log('State verification:', {\n        receivedState: state,\n        storedState,\n        match: storedState === state\n      });\n\n      if (!storedState || storedState !== state) {\n        console.error('State parameter mismatch:', { receivedState: state, storedState });\n        throw new Error('Invalid state parameter');\n      }\n      \n      // Clean up stored state\n      localStorage.removeItem('facebook_oauth_state');\n      \n      const redirectUri = `${window.location.origin}/`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      \n      if (response.data.user && response.data.tokens) {\n        // Store tokens and user data\n        localStorage.setItem('accessToken', response.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        \n        // Update auth context\n        window.location.reload(); // Simple way to update the auth context\n        \n        toast.success('Successfully logged in with Facebook!');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      \n      // Clean up URL parameters\n      const url = new URL(window.location);\n      url.searchParams.delete('code');\n      url.searchParams.delete('state');\n      window.history.replaceState({}, document.title, url.toString());\n    }\n  };\n\n  if (isAuthenticated) {\n    return (\n      <div className=\"auth-section\">\n        <h2>Authentication Status</h2>\n        <div className=\"auth-status authenticated\">\n          <div className=\"user-info\">\n            <User size={20} />\n            <div>\n              <p><strong>{user.firstName} {user.lastName}</strong></p>\n              <p>{user.email}</p>\n              <p>Role: {user.role}</p>\n              {user.facebookConnected && (\n                <p style={{color: '#1877f2'}}>✅ Facebook Connected</p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"token-section\">\n            <div className=\"token-header\">\n              <span>JWT Token:</span>\n              <button \n                onClick={() => setShowToken(!showToken)}\n                className=\"toggle-token\"\n              >\n                {showToken ? <EyeOff size={16} /> : <Eye size={16} />}\n              </button>\n            </div>\n            {showToken && (\n              <div className=\"token-display\">\n                <code>{getToken()}</code>\n              </div>\n            )}\n          </div>\n\n          <button onClick={logout} className=\"logout-btn\">\n            <LogOut size={16} />\n            Logout\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-section\">\n      <h2>Authentication</h2>\n      \n      <div className=\"auth-tabs\">\n        <button \n          className={activeTab === 'facebook' ? 'active' : ''}\n          onClick={() => setActiveTab('facebook')}\n        >\n          <Facebook size={16} />\n          Facebook Login\n        </button>\n        <button \n          className={activeTab === 'login' ? 'active' : ''}\n          onClick={() => setActiveTab('login')}\n        >\n          Email Login\n        </button>\n        <button \n          className={activeTab === 'register' ? 'active' : ''}\n          onClick={() => setActiveTab('register')}\n        >\n          Register\n        </button>\n      </div>\n\n      {activeTab === 'facebook' ? (\n        <div className=\"facebook-login-section\">\n          <div className=\"facebook-login-info\">\n            <h3>\n              <Facebook size={20} />\n              Login with Facebook\n            </h3>\n            <p>Connect your Facebook Business Manager account to access:</p>\n            <ul>\n              <li>✅ Ad account management</li>\n              <li>✅ Campaign creation and monitoring</li>\n              <li>✅ Lead generation forms</li>\n              <li>✅ Marketing API permissions</li>\n              <li>✅ No separate email/password required</li>\n            </ul>\n          </div>\n          \n          <button \n            onClick={handleFacebookLogin}\n            disabled={loading}\n            className=\"facebook-login-btn\"\n          >\n            <Facebook size={20} />\n            {loading ? 'Connecting to Facebook...' : 'Login with Facebook Business Manager'}\n          </button>\n          \n          <p className=\"facebook-note\">\n            Using App ID: <code>***************</code><br/>\n            This will automatically grant marketing API permissions.\n          </p>\n        </div>\n      ) : activeTab === 'login' ? (\n        <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...loginForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {loginForm.formState.errors.email && (\n              <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...loginForm.register('password', { required: 'Password is required' })}\n              placeholder=\"Enter your password\"\n            />\n            {loginForm.formState.errors.password && (\n              <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n      ) : (\n        <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>First Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('firstName', { required: 'First name is required' })}\n              placeholder=\"Enter your first name\"\n            />\n            {registerForm.formState.errors.firstName && (\n              <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Last Name:</label>\n            <input\n              type=\"text\"\n              {...registerForm.register('lastName', { required: 'Last name is required' })}\n              placeholder=\"Enter your last name\"\n            />\n            {registerForm.formState.errors.lastName && (\n              <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email:</label>\n            <input\n              type=\"email\"\n              {...registerForm.register('email', { required: 'Email is required' })}\n              placeholder=\"Enter your email\"\n            />\n            {registerForm.formState.errors.email && (\n              <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password:</label>\n            <input\n              type=\"password\"\n              {...registerForm.register('password', { \n                required: 'Password is required',\n                minLength: { value: 8, message: 'Password must be at least 8 characters' }\n              })}\n              placeholder=\"Enter your password (min 8 characters)\"\n            />\n            {registerForm.formState.errors.password && (\n              <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Phone (optional):</label>\n            <input\n              type=\"tel\"\n              {...registerForm.register('phone')}\n              placeholder=\"Enter your phone number\"\n            />\n          </div>\n\n          <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n            {loading ? 'Registering...' : 'Register'}\n          </button>\n        </form>\n      )}\n    </div>\n  );\n};\n\nexport default AuthSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AAClE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC9E,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM2B,SAAS,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,YAAY,GAAGzB,OAAO,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,MAAM4B,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpC,IAAID,IAAI,IAAIE,KAAK,IAAI,CAACpB,eAAe,EAAE;MACrCqB,sBAAsB,CAACH,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACpB,eAAe,CAAC,CAAC;EAErB,MAAMsB,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9Bb,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMT,KAAK,CAACsB,IAAI,CAAC;IACjBb,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMc,UAAU,GAAG,MAAOD,IAAI,IAAK;IACjCb,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMR,QAAQ,CAACqB,IAAI,CAAC;IACpBb,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMe,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,WAAW,GAAG,GAAGX,MAAM,CAACC,QAAQ,CAACW,MAAM,GAAG;MAChD,MAAMC,QAAQ,GAAG,MAAMxC,WAAW,CAACyC,WAAW,CAACH,WAAW,CAAC;MAE3D,IAAIE,QAAQ,CAACL,IAAI,CAACO,QAAQ,EAAE;QAC1B;QACAC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEJ,QAAQ,CAACL,IAAI,CAACH,KAAK,CAAC;QACrE;QACAa,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;QAC/CD,YAAY,CAACE,OAAO,CAAC,sBAAsB,EAAEP,QAAQ,CAACL,IAAI,CAACH,KAAK,CAAC;QACjEL,MAAM,CAACC,QAAQ,CAACoB,IAAI,GAAGR,QAAQ,CAACL,IAAI,CAACO,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIO,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd5B,UAAU,CAAC,KAAK,CAAC;MACjBhB,KAAK,CAAC4C,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAACC,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMlB,sBAAsB,GAAG,MAAAA,CAAOH,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM8B,WAAW,GAAGP,YAAY,CAACQ,OAAO,CAAC,sBAAsB,CAAC;MAChEV,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCU,aAAa,EAAEtB,KAAK;QACpBoB,WAAW;QACXG,KAAK,EAAEH,WAAW,KAAKpB;MACzB,CAAC,CAAC;MAEF,IAAI,CAACoB,WAAW,IAAIA,WAAW,KAAKpB,KAAK,EAAE;QACzCW,OAAO,CAACO,KAAK,CAAC,2BAA2B,EAAE;UAAEI,aAAa,EAAEtB,KAAK;UAAEoB;QAAY,CAAC,CAAC;QACjF,MAAM,IAAIH,KAAK,CAAC,yBAAyB,CAAC;MAC5C;;MAEA;MACAJ,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;MAE/C,MAAMR,WAAW,GAAG,GAAGX,MAAM,CAACC,QAAQ,CAACW,MAAM,GAAG;MAChD,MAAMC,QAAQ,GAAG,MAAMxC,WAAW,CAACwD,mBAAmB,CAAC1B,IAAI,EAAEE,KAAK,EAAEM,WAAW,CAAC;MAEhF,IAAIE,QAAQ,CAACL,IAAI,CAACxB,IAAI,IAAI6B,QAAQ,CAACL,IAAI,CAACsB,MAAM,EAAE;QAC9C;QACAZ,YAAY,CAACE,OAAO,CAAC,aAAa,EAAEP,QAAQ,CAACL,IAAI,CAACsB,MAAM,CAACC,WAAW,CAAC;QACrEb,YAAY,CAACE,OAAO,CAAC,cAAc,EAAEP,QAAQ,CAACL,IAAI,CAACsB,MAAM,CAACE,YAAY,CAAC;QACvEd,YAAY,CAACE,OAAO,CAAC,MAAM,EAAEa,IAAI,CAACC,SAAS,CAACrB,QAAQ,CAACL,IAAI,CAACxB,IAAI,CAAC,CAAC;;QAEhE;QACAgB,MAAM,CAACC,QAAQ,CAACkC,MAAM,CAAC,CAAC,CAAC,CAAC;;QAE1BxD,KAAK,CAACyD,OAAO,CAAC,uCAAuC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd5B,UAAU,CAAC,KAAK,CAAC;MACjBhB,KAAK,CAAC4C,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAACC,OAAO,CAAC;;MAEtD;MACA,MAAMa,GAAG,GAAG,IAAIC,GAAG,CAACtC,MAAM,CAACC,QAAQ,CAAC;MACpCoC,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;MAC/BH,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;MAChCxC,MAAM,CAACyC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEP,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;EAED,IAAI5D,eAAe,EAAE;IACnB,oBACEJ,OAAA;MAAKiE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BlE,OAAA;QAAAkE,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BtE,OAAA;QAAKiE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxClE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlE,OAAA,CAACP,IAAI;YAAC8E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClBtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAAkE,QAAA,eAAGlE,OAAA;gBAAAkE,QAAA,GAAS/D,IAAI,CAACqE,SAAS,EAAC,GAAC,EAACrE,IAAI,CAACsE,QAAQ;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDtE,OAAA;cAAAkE,QAAA,EAAI/D,IAAI,CAACuE;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBtE,OAAA;cAAAkE,QAAA,GAAG,QAAM,EAAC/D,IAAI,CAACwE,IAAI;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvBnE,IAAI,CAACyE,iBAAiB,iBACrB5E,OAAA;cAAG6E,KAAK,EAAE;gBAACC,KAAK,EAAE;cAAS,CAAE;cAAAZ,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BlE,OAAA;YAAKiE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlE,OAAA;cAAAkE,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBtE,OAAA;cACE+E,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,CAACD,SAAS,CAAE;cACxCsD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAEvBvD,SAAS,gBAAGX,OAAA,CAACJ,MAAM;gBAAC2E,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGtE,OAAA,CAACL,GAAG;gBAAC4E,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACL3D,SAAS,iBACRX,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BlE,OAAA;cAAAkE,QAAA,EAAO1D,QAAQ,CAAC;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENtE,OAAA;UAAQ+E,OAAO,EAAExE,MAAO;UAAC0D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC7ClE,OAAA,CAACN,MAAM;YAAC6E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtE,OAAA;IAAKiE,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BlE,OAAA;MAAAkE,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvBtE,OAAA;MAAKiE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlE,OAAA;QACEiE,SAAS,EAAExD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDsE,OAAO,EAAEA,CAAA,KAAMrE,YAAY,CAAC,UAAU,CAAE;QAAAwD,QAAA,gBAExClE,OAAA,CAACH,QAAQ;UAAC0E,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtE,OAAA;QACEiE,SAAS,EAAExD,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;QACjDsE,OAAO,EAAEA,CAAA,KAAMrE,YAAY,CAAC,OAAO,CAAE;QAAAwD,QAAA,EACtC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtE,OAAA;QACEiE,SAAS,EAAExD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpDsE,OAAO,EAAEA,CAAA,KAAMrE,YAAY,CAAC,UAAU,CAAE;QAAAwD,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL7D,SAAS,KAAK,UAAU,gBACvBT,OAAA;MAAKiE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrClE,OAAA;QAAKiE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClClE,OAAA;UAAAkE,QAAA,gBACElE,OAAA,CAACH,QAAQ;YAAC0E,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtE,OAAA;UAAAkE,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAAkE,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCtE,OAAA;YAAAkE,QAAA,EAAI;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CtE,OAAA;YAAAkE,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCtE,OAAA;YAAAkE,QAAA,EAAI;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCtE,OAAA;YAAAkE,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENtE,OAAA;QACE+E,OAAO,EAAElD,mBAAoB;QAC7BmD,QAAQ,EAAEnE,OAAQ;QAClBoD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAE9BlE,OAAA,CAACH,QAAQ;UAAC0E,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBzD,OAAO,GAAG,2BAA2B,GAAG,sCAAsC;MAAA;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAETtE,OAAA;QAAGiE,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,gBACb,eAAAlE,OAAA;UAAAkE,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAAAtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,4DAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,GACJ7D,SAAS,KAAK,OAAO,gBACvBT,OAAA;MAAMiF,QAAQ,EAAElE,SAAS,CAACmE,YAAY,CAACxD,OAAO,CAAE;MAACuC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpElE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBtE,OAAA;UACEmF,IAAI,EAAC,OAAO;UAAA,GACRpE,SAAS,CAACT,QAAQ,CAAC,OAAO,EAAE;YAAE8E,QAAQ,EAAE;UAAoB,CAAC,CAAC;UAClEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDvD,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACb,KAAK,iBAC/B1E,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEnD,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACb,KAAK,CAAC/B;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBtE,OAAA;UACEmF,IAAI,EAAC,UAAU;UAAA,GACXpE,SAAS,CAACT,QAAQ,CAAC,UAAU,EAAE;YAAE8E,QAAQ,EAAE;UAAuB,CAAC,CAAC;UACxEC,WAAW,EAAC;QAAqB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDvD,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACC,QAAQ,iBAClCxF,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEnD,SAAS,CAACuE,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAC7C;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAQmF,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAEnE,OAAQ;QAACoD,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DrD,OAAO,GAAG,eAAe,GAAG;MAAO;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEPtE,OAAA;MAAMiF,QAAQ,EAAEjE,YAAY,CAACkE,YAAY,CAACtD,UAAU,CAAE;MAACqC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC1ElE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BtE,OAAA;UACEmF,IAAI,EAAC,MAAM;UAAA,GACPnE,YAAY,CAACV,QAAQ,CAAC,WAAW,EAAE;YAAE8E,QAAQ,EAAE;UAAyB,CAAC,CAAC;UAC9EC,WAAW,EAAC;QAAuB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDtD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACf,SAAS,iBACtCxE,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAElD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACf,SAAS,CAAC7B;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAChF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzBtE,OAAA;UACEmF,IAAI,EAAC,MAAM;UAAA,GACPnE,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YAAE8E,QAAQ,EAAE;UAAwB,CAAC,CAAC;UAC5EC,WAAW,EAAC;QAAsB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACDtD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACd,QAAQ,iBACrCzE,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAElD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACd,QAAQ,CAAC9B;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrBtE,OAAA;UACEmF,IAAI,EAAC,OAAO;UAAA,GACRnE,YAAY,CAACV,QAAQ,CAAC,OAAO,EAAE;YAAE8E,QAAQ,EAAE;UAAoB,CAAC,CAAC;UACrEC,WAAW,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDtD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACb,KAAK,iBAClC1E,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAElD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACb,KAAK,CAAC/B;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC5E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBtE,OAAA;UACEmF,IAAI,EAAC,UAAU;UAAA,GACXnE,YAAY,CAACV,QAAQ,CAAC,UAAU,EAAE;YACpC8E,QAAQ,EAAE,sBAAsB;YAChCK,SAAS,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAE/C,OAAO,EAAE;YAAyC;UAC3E,CAAC,CAAC;UACF0C,WAAW,EAAC;QAAwC;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EACDtD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACC,QAAQ,iBACrCxF,OAAA;UAAMiE,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAElD,YAAY,CAACsE,SAAS,CAACC,MAAM,CAACC,QAAQ,CAAC7C;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA;UAAAkE,QAAA,EAAO;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCtE,OAAA;UACEmF,IAAI,EAAC,KAAK;UAAA,GACNnE,YAAY,CAACV,QAAQ,CAAC,OAAO,CAAC;UAClC+E,WAAW,EAAC;QAAyB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtE,OAAA;QAAQmF,IAAI,EAAC,QAAQ;QAACH,QAAQ,EAAEnE,OAAQ;QAACoD,SAAS,EAAC,YAAY;QAAAC,QAAA,EAC5DrD,OAAO,GAAG,gBAAgB,GAAG;MAAU;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpE,EAAA,CA3SID,WAAW;EAAA,QACsDX,OAAO,EAK1DC,OAAO,EACJA,OAAO;AAAA;AAAAoG,EAAA,GAPxB1F,WAAW;AA6SjB,eAAeA,WAAW;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}